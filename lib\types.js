const lotSituation = {
  pending: 'PENDING',
  processing: 'PROCESSING',
  processed: 'PROCESSED',
  failed: 'FAILED',
  success: 'SUCCESS',
  partial_failed: 'PARTIAL_FAILED',
};

const transactionSituation = {
  pending: 'Pendente',
  failed: 'Falhado',
  active: 'Ativo', // Venceu e não foi pago
  to_due: 'A vencer',
  active_due: 'Ativo/A vencer', // usado no front, inútil pra mim
  canceled: 'Cancelado',
  paid: 'Pago',
  canceled_agreement: 'Cancelado por acordo',
};

const bankslipTypes = {
  single: 'Avulso',
  monthly: 'Mensal',
  agreement: 'Acordo',
};

const isRegistered = {
  yes: 'sim',
  no: 'nao',
  error: 'erro',
};

const lotEmailReceiverType = {
  tenant: 'Inquilinos',
  owner: 'Proprietários',
  all: 'Todos',
  bankslip: 'Boleto',
};

const sqsProcessType = {
  single: 'Avulso',
  monthly: 'Mensal',
  massive: 'Email em massa',
  emails: 'Emails',
  agreement: 'Acordo',
  cancel_agreement: 'Cancelamento de acordo',
};

const hastags = [
  '#nome',
  '#unidade',
  '#condominio',
  '#bloco',
  '#boleto_aberto',
  '#endereco',
  '#tel',
  '#boleto_acordo',
];

const emailStatus = {
  pending: 'Pendente',
  sent: 'Enviado',
  save: 'Salvo',
  failed: 'Falha ao enviar',
};

const states = {
  AC: 'Acre',
  AL: 'Alagoas',
  AP: 'Amapá',
  AM: 'Amazonas',
  BA: 'Bahia',
  CE: 'Ceará',
  DF: 'Distrito Federal',
  ES: 'Espirito Santo',
  GO: 'Goiás',
  MA: 'Maranhão',
  MS: 'Mato Grosso do Sul',
  MT: 'Mato Grosso',
  MG: 'Minas Gerais',
  PA: 'Pará',
  PB: 'Paraíba',
  PR: 'Paraná',
  PE: 'Pernambuco',
  PI: 'Piauí',
  RJ: 'Rio de Janeiro',
  RN: 'Rio Grande do Norte',
  RS: 'Rio Grande do Sul',
  RO: 'Rondônia',
  RR: 'Roraima',
  SC: 'Santa Catarina',
  SP: 'São Paulo',
  SE: 'Sergipe',
  TO: 'Tocantins',
};

const webhookType = {
  celcoin: 'celcoin',
  email: 'email',
};

const emailTemplates = {
  info: 'd-c75a0e4e8e00463584743a91ccc13121',
  bankslip: 'd-e95236f67a6a4528867ded78f8c54e2c',
};

const emailWebhookEvents = {
  open: 'open',
  clicked: 'clicked',
};

const translateCelcoinStatusToOurStatus = {
  active: transactionSituation['active'],
  canceled: transactionSituation['canceled'],
  closed: transactionSituation['paid'],
  waitingPayment: transactionSituation['to_due'],
  inactive: transactionSituation['canceled'],
};

const notificationSendTo = {
  resident: 'm',
  union: 's',
  all: 'a',
};

const notificationTopic = {
  m: 'morador',
  s: 'sindico',
};

const agreementSituation = {
  active: 'Ativo',
  processing: 'Processando',
  canceled: 'Cancelado',
};

const xslxImportTypes = {
  unit: 'unit',
  agreement: 'agreement',
  default: 'default',
};

const unitSheetTemplateKeys = {
  nome_unidade: 'unit',
  bloco_unidade: 'block',
  vencimento_diferente_unidade: 'differentMaturity',
  valor_diferente_unidade: 'payDifferentAmmount',
  unidade_isenta: 'immunity',
  nome_dono: 'ownerName',
  documento_dono: 'ownerRegister',
  email_dono: 'ownerEmail',
  celular_dono: 'ownerCel',
  rua: 'ownerAdress',
  numero_casa: 'ownerHouseNumber',
  bairro: 'ownerNeighborhood',
  cidade: 'ownerCity',
  estado: 'ownerState',
  cep: 'ownerZipCode',
};

const agreementSheetTemplateKeys = {
  'ID Pagador': 'payerId',
  'Valor Principal do Acordo': 'ammount',
  'Valor de Entrada': 'inflow',
  'Data da Entrada': 'inflowDate',
  'Despesas Adicionais': 'expenses',
  'Valor de Correção/Índice': 'indexValue',
  'Número de Parcelas': 'parcels',
  'Vencimento da Primeira Parcela': 'maturity',
  'Multa por Atraso (%)': 'fine',
  'Juros ao Mês (%)': 'fees',
  'Aplicar Multa e Juros?': 'feesAndFine',
  'Enviar Email?': 'sendEmail',
  'Emissor': 'issuer',
  'Mês de Referência da Cobrança': 'monthCharge',
  'Registrar Boletos?': 'isRegistered',
};


const reportKeysType = {
  bankslip: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    emissionType: 'Todos | Avulso | Mensal | Acordo',
    situation: '',
    ourNumber: '',
    sintetic: true,
  },
  resident: {
    ownerData: true,
    what: ['iq', 'ib', 'hs'], // inquilino, imobiliaria, historico
    list: true,
    who: ['iq', 'cj'], // inquilino, conjuge
  },
  agreement: {
    interval: {
      start: '',
      end: '',
    },
    canceled: false, // se são os acordos cancelados ou não
    extras: ['add', 'hs', 'anx'], // Acrescimos, Histórico, Anexo
  },
  expanses: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    category: '',
    subcategory: '',
    bankingAccount: '',
    keyWord: '',
    desciption: false,
  },
  dre: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    extras: ['si', 'sc', 'dt'], // Saldo Inicial/Final, Saldo por conta, Detalhado
  },
  dailyBook: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    accounting: '',
    extras: ['si', 'sc', 'dt'], // Saldo Inicial/Final, Saldo por conta, Detalhado
  },
  default: {
    // inadimplencia
    interval: {
      start: '',
      end: '',
    },
    type: '"" | ad | cnd', // vazio, adimplentes, certidão negativa de débitos
    extras: ['dt', 'sp', 'oa'], // Detalhado, Separar por página, Outros Acréssimos
    additional: 'tc | ha', //  Taxa Adm. Cobrança, Honorários Advocaticos
    value: 100,
    percent: 100,
    feesAndFine: false,
  },
  email: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    // boleto, multa, convite perfil, termo acordo, cobrança, comunicado assembleia, adventencia
    model: 'bl | mt | cp | ta | cb | cm | as | ad',
    extras: ['as', 'tx'], // assunto, texto
  },
  concierge: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    extras: ['cd', 'un', 'dt'], // Condominio, Unidade, Detalhado
  },
  bankingAccount: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    account: '',
    extras: ['si', 'sc', 'dt'],
  },
  balanceSheetStatement: {
    reference: '',
    cover: '', // 1, 2, 3
    bankslips: {
      active: false,
      paid: false,
    },
    expanses: '', // img,
    dre: false,
    dailyBook: false,
    agreement: false,
    default: false,
  },
  revenue: {
    reference: '',
    interval: {
      start: '',
      end: '',
    },
    account: '',
    accounting: '',
    type: 't | m | a', // todas, manual, automatico
    desciption: false,
  },
};

module.exports = {
  lotSituation,
  transactionSituation,
  bankslipTypes,
  isRegistered,
  lotEmailReceiverType,
  sqsProcessType,
  hastags,
  emailStatus,
  states,
  webhookType,
  emailTemplates,
  emailWebhookEvents,
  translateCelcoinStatusToOurStatus,
  notificationSendTo,
  notificationTopic,
  agreementSituation,
  xslxImportTypes,
  unitSheetTemplateKeys,
  agreementSheetTemplateKeys,
};
