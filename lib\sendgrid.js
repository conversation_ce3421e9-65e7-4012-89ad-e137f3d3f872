const sgMail = require('@sendgrid/mail');
sgMail.setApiKey(process.env.CONDOGAIA_EMAIL);
const { createSentEmail } = require('./firestore');

// Manda email para o usuário e salva o email no banco de dados
const sendEmail = async (send = false, cond_uuid, email_id, msg, db_data) => {
    try {
        console.log('Enviando email...');
        if (send) {
            await sgMail.send(msg).catch((error) => {
                console.error(error);
                if (error.response) {
                    console.log('Deu erro ao enviar o email');
                    throw error.response.body;
                }
            });
        } else {
            const err = await createSentEmail(cond_uuid, email_id, db_data);

            if (err) {
                throw 'O email não foi salvo no banco de dados';
            }
        }

        return undefined;
    } catch (e) {
        return e;
    }
};

module.exports = { sendEmail };
