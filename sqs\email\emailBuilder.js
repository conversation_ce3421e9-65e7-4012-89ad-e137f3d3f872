const { now, emailTemplates } = require('../../lib');

function buildEmail(unitData, condData, lotData, text, emailId, to, name, isOwner) {
  return {
    to,
    from: {
      email: '<EMAIL>',
      name: 'Condog<PERSON>',
    },
    subject: lotData.subject,
    templateId: emailTemplates.info || 'default-template',
    dynamicTemplateData: {
      block: unitData.block,
      unit: unitData.unit,
      logo1: condData.logo1 || '',
      logo2: condData.logo2 || '',
      name,
      subject: lotData.subject,
      date: now('DD/MM/YYYY'),
      email: to,
      content: text,
      condName: condData.condName,
    },
    category: `${condData.cond_uuid || 'condominio'}:${emailId}`,
    attachments: [], // será preenchido depois
  };
}

module.exports = { buildEmail };
