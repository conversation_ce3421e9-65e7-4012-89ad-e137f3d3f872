const { v4: uuidv4 } = require('uuid');
const { replaceTags, buildAttachment, buildEmail, sendAndLog } = require('./email');
const { firestore, now, emailStatus } = require('../lib');

async function processMassEmail(cond_uuid, lot_uuid, lotData, condData) {
  try {
    const receiversSnap = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('emails-lots')
      .doc(lot_uuid)
      .collection('receivers')
      .get();

    if (receiversSnap.empty) {
      console.log(`Nenhum destinatário encontrado para o lote ${lot_uuid}`);
      return;
    }

    for (const doc of receiversSnap.docs) {
      const receiver = doc.data();
      const emailId = uuidv4();
      const text = replaceTags(lotData.text || '', receiver, condData);
      const attachment = await buildAttachment(lotData.file);

      const msg = buildEmail(receiver, condData, lotData, text, emailId, receiver.email, receiver.name);
      msg.attachments = attachment;

      const dbSave = {
        emailId,
        unitId: receiver.unitId,
        unitName: receiver.unitName,
        unitBlock: receiver.unitBlock,
        receiverName: receiver.name,
        emailModel: lotData.topic,
        email: receiver.email,
        topic: lotData.topic,
        subject: lotData.subject,
        text,
        situation: emailStatus.pending,
        file: lotData.file?.file || '',
        clicked: false,
        opened: false,
        sentAt: now(),
        created_at: now(),
        updated_at: now(),
      };

      await sendAndLog(cond_uuid, lot_uuid, emailId, msg, dbSave);
    }

    console.log(`Emails do lote ${lot_uuid} enviados com sucesso.`);
  } catch (e) {
    console.error('Erro ao processar envio em massa:', e);
  }
}

module.exports = { processMassEmail };
