const axios = require('axios');
const { celcashHeader } = require('./connect-cel-cash');
const { allowedMethods } = require('./types');
const baseURL = process.env.CELCASH_BASE_URL;

// Executa uma requisição pra celcoin com headers com token do condominio se não for passado um pela requisição
const http = async (method = 'get', url, headers = undefined, body = {}) => {
    try {
        if (!allowedMethods[method]) {
            console.log(`Method not allowed: ${method}`);
            throw `Method not allowed: ${method}`;
        }

        let defaultHeaders;
        if (!headers) {
            const { header, ch_error } = await celcashHeader(
                process.env.GALAXY_ID,
                process.env.GALAXY_HASH
            );

            if (ch_error) {
                console.log('Deu erro gerando o token na celcoin');
                throw ch_error;
            }

            defaultHeaders = header;
        }

        const resp = await axios({
            method: allowedMethods[method],
            url: `${baseURL}${url}`,
            data: body,
            headers: headers ? headers : defaultHeaders,
        });

        if (!resp.status || resp.status > 299) {
            throw resp;
        }

        return { resp: resp.data };
    } catch (e) {
        return { err: e };
    }
};

module.exports = { http };
