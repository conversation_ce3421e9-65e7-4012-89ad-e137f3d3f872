const {
    notify,
    notificationSendTo,
    notificationTopic,
} = require('../../../lib');

module.exports = async (req) => {
    try {
        console.log('Processando api de notificações');
        const { body } = req;

        const invalid = validate(body);

        if (invalid) {
            console.log('Bad request:', invalid);
            return {
                status: 400,
                code: 'MS00051',
                result: invalid,
            };
        }

        let err = undefined;

        if (body.sendTo === notificationSendTo.all) {
            if (body.condId) {
                err = await notify(
                    body.title,
                    body.message,
                    `${body.condId}.${notificationTopic.m}`
                );

                err = await notify(
                    body.title,
                    body.message,
                    `${body.condId}.${notificationTopic.s}`
                );

                if (err) {
                    console.log('Deu erro tentando enviar a notificação');
                    console.log(err);
                    return {
                        status: 500,
                        code: 'MS00052',
                        result: 'Erro ao tentar enviar a notificação, tente novamente mais tarde',
                    };
                }
            } else {
                err = await notify(
                    body.title,
                    body.message,
                    notificationTopic.m
                );

                err = await notify(
                    body.title,
                    body.message,
                    notificationTopic.s
                );

                if (err) {
                    console.log('Deu erro tentando enviar a notificação');
                    console.log(err);
                    return {
                        status: 500,
                        code: 'MS00053',
                        result: 'Erro ao tentar enviar a notificação, tente novamente mais tarde',
                    };
                }
            }
        } else {
            if (body.condId) {
                err = await notify(
                    body.title,
                    body.message,
                    `${body.condId}.${notificationTopic[body.sendTo]}`
                );

                if (err) {
                    console.log('Deu erro tentando enviar a notificação');
                    console.log(err);
                    return {
                        status: 500,
                        code: 'MS00054',
                        result: 'Erro ao tentar enviar a notificação, tente novamente mais tarde',
                    };
                }
            } else {
                err = await notify(
                    body.title,
                    body.message,
                    notificationTopic[body.sendTo]
                );

                if (err) {
                    console.log('Deu erro tentando enviar a notificação');
                    console.log(err);
                    return {
                        status: 500,
                        code: 'MS00055',
                        result: 'Erro ao tentar enviar a notificação, tente novamente mais tarde',
                    };
                }
            }
        }

        return {
            status: 200,
            result: 'As notificações foram enviadas com sucesso',
        };
    } catch (e) {
        console.log(e);
        return {
            status: 500,
            code: 'MS00050',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};

const validate = (body) => {
    if (!body.title) {
        return 'O título é obrigatório';
    }
    if (!body.message) {
        return 'A mensagem é obrigatória';
    }
    if (!body.sendTo) {
        return 'O destinatário é obrigatório';
    }

    return undefined;
};
