const responseWith = async (req, res, next, func) => {
    try {
        console.log('Inicio da execução da api...');

        const resp = await func(req);

        console.log('Fim da execução da api...');
        if (resp.status <= 299) {
            res.status(resp.status).send(resp.result);
            return;
        }
        res.status(resp.status).send({ result: resp.result, code: resp.code });
    } catch (e) {
        console.log(e);
        next(e);
    }
};

const responseWithWebhook = async (req, res, next, func) => {
    try {
        await func(req, res);

        next();
    } catch (e) {
        console.log(e);
        next(e);
    }
};

module.exports = { responseWith, responseWithWebhook };
