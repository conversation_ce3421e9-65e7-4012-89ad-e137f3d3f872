const {
    getLotByID,
    sendMessage,
    lotEmailReceiverType,
    lotSituation,
    sqsProcessType,
} = require('../../../lib');

module.exports = async (req) => {
    try {
        console.log('Email em massa');
        const { headers, body } = req;
        const cid = headers['x-cid'];
        const lid = body.lot_id;

        if (!lid) {
            console.log('Sem o id do lote: ', lid);
            return {
                status: 400,
                code: 'MS0018',
                result: 'O id do lote é obrigatório',
            };
        }

        const { lotData, gl_error } = await getLotByID(
            cid,
            lid,
            sqsProcessType.emails
        );

        if (gl_error) {
            console.log(
                'Deu erro buscando o lote. ID do lote:',
                lid,
                ' ID do condominio:',
                cid
            );
            console.log(gl_error);
            return {
                status: 500,
                code: 'MS0019',
                result: 'Ocorreu um erro ao tentar buscar o lote dos emails, tente novamente mais tarde',
            };
        }

        if (lotData.situation !== lotSituation.pending) {
            console.log(lotData.situation);
            return {
                status: 201,
                result: 'O lote já está sendo processado',
            };
        }

        if (
            lotData.receiverType !== lotEmailReceiverType.tenant &&
            lotData.receiverType !== lotEmailReceiverType.owner &&
            lotData.receiverType !== lotEmailReceiverType.all
        ) {
            console.log(
                'O tipo de recebedor do email não é valido:',
                lotData.receiverType
            );
            return {
                status: 400,
                code: 'MS0020',
                result: `O tipo de recebedor do email não é valido: ${lotData.receiverType}`,
            };
        }

        err = await sendMessage(lid, cid, sqsProcessType.emails, {});

        if (err) {
            console.log(err);
            return {
                status: 500,
                code: 'MS00021',
                result: 'Ocorreu um erro ao tentar mandar os dados pra fila de processamento',
            };
        }

        return {
            status: 201,
            result: 'Os emails estão em fila de processamento e logo serão enviados',
        };
    } catch (e) {
        console.log('Deu na api de mandar os lotes de email em massa pro sqs');
        console.log(e);
        return {
            status: 500,
            code: 'MS0017',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};

// #boleto_aberto - vencido - data de venc e valor

// planilha posso gerar de modelo de implantação
