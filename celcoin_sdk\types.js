const mainPaymentMethodId = {
    creditcard: 'creditcard',
    boleto: 'boleto',
    pix: 'pix',
};

const allowedMethods = {
    get: 'GET',
    post: 'POST',
    patch: 'PATCH',
    put: 'PUT',
    delete: 'DELETE',
};

const webhookEvents = {
    transaction: 'transaction.updateStatus',
    accountDocuments: 'company.verifyDocuments',
};

const chargeStatus = {
    active: 'active', // Ativa
    canceled: 'canceled', // Cancelada
    closed: 'closed', // Encerrada
    waitingPayment: 'waitingPayment', // Aguardando pagamento
    inactive: 'inactive', // Inativa
};

const transactionStatus = {
    bankslip: {
        pending: 'pendingBoleto', // Boleto em aberto
        payed: 'payedBoleto', // Boleto compensado
        notCompensated: 'notCompensated', // Boleto baixado por decurso de prazo
        lessValueBoleto: 'lessValueBoleto', // Pago valor menor que o original
        moreValueBoleto: 'moreValueBoleto', // Pago valor maior que o original
        paidDuplicityBoleto: 'paidDuplicityBoleto', // Pago em duplicidade
    },
    pix: {
        pending: 'pendingPix', // Pix em aberto
        payed: 'payedPix', // Pix pago
        unavailable: 'unavailablePix', // Pix indis*ponível para pagamento
    },
    all: {
        cancel: 'cancel', // Cancelada manualmente
        payExternal: 'payExternal', // Paga fora do sistema
        cancelByContract: 'cancelByContract', // Cancelada ao cancelar a cobrança
        free: 'free', // Isento
    },
};

module.exports = {
    mainPaymentMethodId,
    allowedMethods,
    webhookEvents,
    chargeStatus,
    transactionStatus,
};
