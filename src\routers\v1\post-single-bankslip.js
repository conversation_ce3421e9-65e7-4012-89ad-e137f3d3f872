const {
    updateLotByID,
    getLotByID,
    sendMessage,
    now,
    bankslipTypes,
    lotSituation,
    sqsProcessType,
} = require('../../../lib');

/*
 * Default module exports
 * Single bankslip - Reposnsável por pegar o lote e mandar as transações pro sqs processar os boletos avulso
 */
module.exports = async (req) => {
    try {
        const { headers, body } = req;
        const cid = headers['x-cid'];
        const lid = body.lot_id;

        if (!lid) {
            return {
                status: 400,
                code: 'MS0013',
                result: 'O id do lote é obrigatório',
            };
        }

        const { lotData, gl_error } = await getLotByID(cid, lid);

        if (gl_error) {
            console.log(
                'Deu erro buscando o lote. ID do lote:',
                lid,
                ' ID do condominio:',
                cid
            );
            console.log(gl_error);
            return {
                status: 500,
                code: 'MS0014',
                result: 'Erro ao tentar buscar informações do lote.',
            };
        }

        const totItemsLength = lotData.itens.length;

        // if (lotData.situation !== lotSituation.pending) {
        //     return {
        //         status: 201,
        //         result: 'O lote já está sendo processado',
        //     };
        // }

        if (!totItemsLength || totItemsLength === 0) {
            // TODO -> Se não tiver item e o tipo for pra todos, buscar todos os payers e adicionar no lote todos os ids
            return {
                status: 400,
                code: 'MS0015',
                result: 'Ta sem nada lá no itens, falta implementar aqui ainda',
            };
        }

        if (lotData.emissionType !== bankslipTypes.single) {
            return {
                status: 400,
                code: 'MS0016',
                result: `O tipo de boleto do lote diferente do esperado: ${lotData.emissionType}`,
            };
        }

        const rest = totItemsLength % 10; // resto da divisão por 10
        const total = (totItemsLength - rest) / 10; // total de itens sem o resto
        const val = rest > 0 ? total + 1 : total; // esse é o numero de lots que serão enviados pro sqs
        let offset = 0; // valor do offset do slice
        let limit = val === 1 ? rest : 10; // valor limite do slice

        let err = await updateLotByID(cid, lid, {
            count: {
                total: totItemsLength,
                processed: 0,
            },
            updated_at: now(),
        });

        if (err) {
            console.log('Deu erro atualizando o lote:', lid);
            throw err;
        }

        for (let i = 1; i <= val; i++) {
            err = await sendMessage(lid, cid, sqsProcessType.single, {
                offset,
                limit,
            });

            if (err) {
                console.log('Deu erro mandando mensagem pro sqs');
                console.log(err);

                return {
                    status: 500,
                    code: 'MS0016',
                    result: 'Ocorreu um erro ao tentar mandar os dados pra fila de processamento',
                };
            }

            console.log('Mensagens enviadas pro sqs:', i);

            offset = offset + 10;
            limit = limit + 10;
        }

        return {
            status: 201,
            result: {
                lot_id: lid,
                message: 'Os boletos estão em fila de processamento',
            },
        };
    } catch (e) {
        console.log(e);
        return {
            status: 500,
            code: 'MS0012',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};
