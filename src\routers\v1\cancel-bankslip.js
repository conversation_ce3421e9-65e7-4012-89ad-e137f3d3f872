const { cancelBankslip } = require('../../../celcoin_sdk');
const {
    getCondByID,
    getTransactionByID,
    updateTransaction,
    now,
    isRegistered,
    transactionSituation,
} = require('../../../lib');

module.exports = async (req) => {
    try {
        console.log('Api de cancelar boleto');
        const { body, headers } = req;

        const cid = headers['x-cid'];
        const transactionsIDs = body.transactionsIds;

        if (!transactionsIDs || transactionsIDs.length === 0) {
            console.log(transactionsIDs);
            return {
                status: 400,
                code: 'MS00041',
                result: 'TransactionId é obrigatório',
            };
        }

        console.log('transactions:', transactionsIDs);

        const { condData, gc_error } = await getCondByID(cid);
        if (gc_error) {
            console.log(gc_error);
            return {
                status: 500,
                code: 'MS00042',
                result: 'Erro ao tentar buscar as informações do condomínio, tente novamente mais tarde',
            };
        }

        for (let i = 0; i < transactionsIDs.length; i++) {
            const myID = transactionsIDs[i];

            if (!myID || myID.length !== 36) {
                console.log('Id do boleto errado:', myID);
                throw 'Id errado do boleto';
            }

            const { transactionData, gt_error } = await getTransactionByID(
                cid,
                myID
            );

            if (gt_error) {
                console.log('Erro ao tentar buscar a transação: ', myID);
                console.log(gt_error);
                throw gt_error;
            }

            if (transactionData.isRegistered === isRegistered.yes) {
                const { result: _, cb_error } = await cancelBankslip({
                    condRegister: condData.condRegister,
                    id: myID,
                });

                if (cb_error) {
                    if (cb_error.response && cb_error.response.data) {
                        console.log(cb_error.response.data);
                    } else {
                        console.log(cb_error);
                    }

                    console.log('Erro ao cancelar boleto na celcoin');
                    return {
                        status: 500,
                        code: 'MS00043',
                        result: 'Ao tentar cancelar o boleto na celcoin, tente novamente mais tarde',
                    };
                }

                console.log('Boleto cancelado na celcoin');
            }

            const er = await updateTransaction(cid, myID, {
                situation: transactionSituation.canceled,
                updated_at: now(),
            });
            if (er) {
                console.log('Error: ', er);
                return {
                    status: 500,
                    code: 'MS00044',
                    result: 'Ao tentar atualizar a transação no banco, tente novamente mais tarde',
                };
            }

            console.log('Boleto cancelado no banco de dados');
        }

        return {
            status: 200,
            result: 'Os boletos foram cancelados com sucesso',
        };
    } catch (e) {
        console.error('Erro na api de cancelar boleto');
        console.error(e);
        return {
            status: 500,
            code: 'MS00040',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};
