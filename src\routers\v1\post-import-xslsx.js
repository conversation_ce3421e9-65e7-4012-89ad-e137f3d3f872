const {
  getSheet,
  xslxImportTypes,
  isEmpty,
  agreementSheetTemplateKeys, // For agreement import
} = require('../../../lib');
const importService = require('../../services/importService');
// unidade
// https://docs.google.com/spreadsheets/d/1vSdKCKIS13S__zaODvfY1rQLjN9pemMHT2QHAgPP1Z0/edit?usp=sharing
// acordo
// https://docs.google.com/spreadsheets/d/1v3z9s9uuA-tQnLxsL3FueqXuKSaIwVZ9kKF7VbGbemc/edit?gid=0#gid=0

const importHandlers = {
  [xslxImportTypes.unit]: async (sheet, cid) => {
    const results = [];
    for (let i = 0; i < sheet.length; i++) {
      const row = sheet[i];
      if (typeof row !== 'object' || row === null) {
        results.push({ error: `Linha ${i + 1} não é um objeto.`, type: 'invalid-row-format' });
        continue;
      }
      const isEmptyRow = Object.keys(row).every(key => isEmpty(row[key]));
      if (isEmptyRow) {
        continue;
      }
      // Remove __EMPTY keys generated by xlsx library for empty header cells
      const cleanedRow = { ...row };
      for (const key in cleanedRow) {
        if (key.startsWith('__EMPTY')) {
          delete cleanedRow[key];
        }
      }
      const result = await importService.processUnitRow(cleanedRow, cid, i);
      results.push(result);
    }
    return results;
  },
  [xslxImportTypes.agreement]: async (sheet, cid) => {
    const results = [];
    for (let i = 0; i < sheet.length; i++) {
      const row = sheet[i];
      if (typeof row !== 'object' || row === null) {
        results.push({ error: `Linha ${i + 1} não é um objeto.`, type: 'invalid-row-format' });
        continue;
      }
      const isEmptyRow = Object.keys(row).every(key => isEmpty(row[key]) || key.startsWith('__EMPTY'));
      if (isEmptyRow) {
        continue;
      }
      const cleanedRow = { ...row };
      for (const key in cleanedRow) {
        if (key.startsWith('__EMPTY')) {
          delete cleanedRow[key];
        }
      }
      const result = await importService.processAgreementRow(cleanedRow, cid, i, agreementSheetTemplateKeys);
      results.push(result);
    }
    return results;
  },
  [xslxImportTypes.default]: async (sheet, cid) => {
    console.log('TODO: Implementar importação de Inadimplência');
    return [{ info: 'Funcionalidade de importação de inadimplência não implementada.' }];
  },
};

module.exports = async (req) => {
  try {
    console.log('Api de importação de planilha rodando...');
    const validationResult = importService.validateRequest(req.body, req.headers);
    if (validationResult.error) {
      console.error(validationResult.error);
      return { status: 400, code: validationResult.code, result: validationResult.error };
    }

    const { cid, url: originalUrl, type: importType } = validationResult;
    const sheetUrl = importService.prepareSheetUrl(originalUrl);

    const { sheet, error: sheetError } = await getSheet(sheetUrl);
    if (sheetError) {
      console.error('Erro ao tentar buscar a planilha:', sheetError);
      return {
        status: 500,
        code: 'MS00084',
        result: 'Erro ao tentar buscar a planilha, tente novamente mais tarde!',
      };
    }

    if (!sheet || sheet.length === 0) {
      console.log('Planilha vazia ou não encontrada.');
      return { status: 400, code: 'MS00085', result: 'Planilha vazia ou não foi possível lê-la.' };
    }

    const handler = importHandlers[importType];

    if (!handler) {
      console.error(`Tipo de importação desconhecido: ${importType}`);
      return { status: 400, code: 'MS00083', result: 'O campo "type" está inválido' };
    }

    const importResults = await handler(sheet, cid); // getSheet already provides usable keys

    const errors = importResults.filter(r => r.error);
    const successes = importResults.filter(r => r.success);

    if (errors.length > 0) {
      console.log(`Importação concluída com ${errors.length} erro(s) e ${successes.length} sucesso(s).`);
      return {
        status: 207, // Multi-Status
        result: {
          message: `Planilha processada com ${errors.length} erro(s) e ${successes.length} sucesso(s).`,
          errors: errors.map(e => ({
            type: e.type,
            details: e.error.message || String(e.error),
            reference: e.unit ? `Unidade: ${e.unit}${e.block ? '/Bloco: ' + e.block : ''}` : (e.payerId ? `Pagador ID: ${e.payerId}` : '')
          })),
          successCount: successes.length,
        }
      };
    }

    return { status: 200, result: `Planilha importada com sucesso. ${successes.length} registro(s) processado(s).` };

  } catch (e) {
    console.error('Erro inesperado durante a importação:', e);
    return {
      status: 500,
      code: 'MS00080',
      result:
        typeof e === 'string'
          ? e
          : 'Problema no servidor interno. Tente novamente mais tarde!',
    };
  }
};
