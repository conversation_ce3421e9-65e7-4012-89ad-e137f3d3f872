const { webhookEvents, chargeStatus } = require('../../../celcoin_sdk');
const {
    getCondIDByConfirmHash,
    getTransactionByID,
    updateTransaction,
    getCondBankAccounts,
    updateAccountDocument,
    now,
    transactionSituation,
    translateCelcoinStatusToOurStatus,
} = require('../../../lib');

module.exports = async (req, res) => {
    try {
        const { body } = req;

        if (!body || !body.event) {
            res.status(400).send('Corpo da requisição vazio');
            return;
        }

        if (body.event === webhookEvents.transaction) {
            console.log('Atualização de transação');
            console.log('BODY: ', body);

            const { cond_id, condRegister, gch_error } =
                await getCondIDByConfirmHash(body.confirmHash);

            if (gch_error) {
                console.log('Deu erro buscando o id do condomínio');
                console.log(gch_error);
                res.status(400).send('Erro ao localizar a subconta');
                return;
            }

            let err = undefined;

            if (body.Charge.status === chargeStatus.canceled) {
                const { transactionData, gt_error } = await getTransactionByID(
                    cond_id,
                    body.Charge.myId
                );

                if (gt_error) {
                    // TODO
                }

                if (
                    transactionData.situation ===
                        transactionSituation.canceled_agreement ||
                    transactionData.situation ===
                        transactionSituation.canceled ||
                    transactionData.situation === transactionSituation.paid
                ) {
                    // TODO
                    console.log(
                        'Boleto já cancelado ou pago na nossa base',
                        transactionData.situation
                    );
                } else {
                    const data = {
                        situation:
                            translateCelcoinStatusToOurStatus[
                                body.Charge.status
                            ],
                        updated_at: now(),
                    };

                    err = updateTransaction(cond_id, body.Charge.myId, data);
                    if (err) {
                        console.log('Erro ao atualizar a transação no banco');
                        res.status(400).send('Erro ao atualizar transação');
                    }
                }
            } else if (body.Charge.status === chargeStatus.closed) {
                const { bankAccounts, bc_error } = await getCondBankAccounts(
                    cond_id,
                    false
                );

                const payload = {
                    key: '***********',
                    type: 'cpf',
                    value: 12999,
                    desc: 'Lorem ipsum dolor sit amet.',
                };
            }

            res.status(200).send('Recebido com sucesso');
            return;
        } else if (body.event === webhookEvents.accountDocuments) {
            console.log('Atualização de aprovação de conta');

            const { Company } = body;

            const { cond_id, condRegister, gch_error } =
                await getCondIDByConfirmHash(body.confirmHash);

            if (gch_error) {
                console.log('Deu erro buscando o id do condomínio');
                console.log(gch_error);
                res.status(400).send('Erro ao localizar a subconta');
                return;
            }

            if (Company.Verification.status === 'approved') {
                const err = await updateAccountDocument(cond_id, {
                    active: true,
                });

                if (err) {
                    console.log('Erro atualizando o status do condomínio');
                    console.error(err);
                    res.status(500).send('Erro ao atualizar a base de dados');
                    return;
                }
            }

            res.status(200).send('Recebido com sucesso');
            return;
        } else {
            console.log(`Evento do webhook não esperado: ${body.event}`);
            res.status(400).send(
                `Evento do webhook não esperado: ${body.event}`
            );
            return;
        }
    } catch (e) {
        console.log('Erro ao receber um webhook da celcoin');
        console.log(e);
        res.status(500).send(
            'Problema no servidor interno. Tente novamente mais tarde!'
        );
    }
};
