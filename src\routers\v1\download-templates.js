const { downloadFile } = require('../../../lib');

// URLs dos templates do Google Sheets
const TEMPLATE_URLS = {
  unit: 'https://docs.google.com/spreadsheets/d/1CnBPtnCwxJ1D1P8zulNQqX-e2dJ0rBUu/export?format=xlsx',
  agreement: 'https://docs.google.com/spreadsheets/d/1k8zO9wc0id-RDlzcM4TTQZt56tVwwwhP/export?format=xlsx'
};

module.exports = async (req) => {
  try {
    console.log('Api de download de templates rodando...');
    
    const { type } = req.query;
    
    // Validar se o tipo foi fornecido
    if (!type) {
      return {
        status: 400,
        code: 'MS00001',
        result: 'Parâmetro "type" é obrigatório. Valores aceitos: unit, agreement'
      };
    }
    
    // Validar se o tipo é válido
    if (!TEMPLATE_URLS[type]) {
      return {
        status: 400,
        code: 'MS00002',
        result: 'Tipo de template inválido. Valores aceitos: unit, agreement'
      };
    }
    
    const templateUrl = TEMPLATE_URLS[type];
    
    try {
      // Fazer o download do arquivo
      const fileBase64 = await downloadFile(templateUrl);
      
      // Definir o nome do arquivo baseado no tipo
      const fileName = type === 'unit' ? 'template_unidades.xlsx' : 'template_acordos.xlsx';
      
      return {
        status: 200,
        result: {
          file: fileBase64,
          fileName: fileName,
          mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      };
      
    } catch (downloadError) {
      console.error('Erro ao fazer download do template:', downloadError);
      return {
        status: 500,
        code: 'MS00003',
        result: 'Erro ao fazer download do template. Tente novamente mais tarde.'
      };
    }
    
  } catch (error) {
    console.error('Erro na API de download de templates:', error);
    return {
      status: 500,
      code: 'MS00004',
      result: 'Erro interno do servidor. Tente novamente mais tarde.'
    };
  }
};
