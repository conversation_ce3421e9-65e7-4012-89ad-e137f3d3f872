const aws = require('aws-sdk');
const { sqsURL } = process.env;

const sqs = new aws.SQS();
sqs.config.update({ region: 'sa-east-1' });

// Responsável por enviar a mensagem pro sqs
const sendMessage = async (uuid, cid, sqs_process_type, config) => {
    return new Promise((resolve, reject) => {
        try {
            sqs.sendMessage(
                {
                    MessageBody: JSON.stringify({
                        lot_uuid: uuid,
                        cond_uuid: cid,
                        process_type: sqs_process_type,
                        config,
                    }),
                    QueueUrl: sqsURL,
                },
                (error, data) => {
                    if (error) {
                        console.log(error);
                        throw error;
                    } else {
                        console.log(
                            `Mensagem enviada com sucesso! ${data.MessageId}`
                        );
                        resolve(undefined);
                    }
                }
            );
        } catch (e) {
            reject(e);
        }
    });
};

// Recebe a mensagem da fila sqs e executa uma função com a mensagem como parâmetro
const receiveMessage = async (func) => {
    try {
        sqs.receiveMessage(
            {
                MaxNumberOfMessages: 10,
                QueueUrl: sqsURL,
                WaitTimeSeconds: 5,
            },
            (error, data) => {
                if (error) {
                    console.log(error);
                    throw error;
                } else if (data.Messages != null && data.Messages.length != 0) {
                    console.log('Mensagem recebida');
                    data.Messages.forEach(async (message) => func(message));
                } else {
                    console.log('Sem dados na fila!');
                }
            }
        );
    } catch (e) {
        return e;
    }
};

// Deleta a mensagem do sqs
const deleteMessage = async (message) => {
    try {
        sqs.deleteMessage(
            {
                QueueUrl: sqsURL,
                ReceiptHandle: message.ReceiptHandle,
            },
            (error, data) => {
                if (error) {
                    console.log('Erro', error);
                    throw error;
                } else {
                    console.log('Deletou com sucesso!');
                }
            }
        );
    } catch (e) {
        return e;
    }
};

module.exports = { sendMessage, receiveMessage, deleteMessage };
