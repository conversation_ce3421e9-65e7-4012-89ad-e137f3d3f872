const { sendEmail, createSentEmail, updateLotByID, now } = require('../../lib');

async function sendAndLog(cond_uuid, lot_uuid, emailId, msg, dbSave) {
  let err = await sendEmail(true, cond_uuid, emailId, msg, dbSave);

  if (err) {
    console.log('Erro ao tentar enviar o email', err);

    await createSentEmail(cond_uuid, emailId, {
      ...dbSave,
      situation: 'FAILED',
    });

    await updateLotByID(
      cond_uuid,
      lot_uuid,
      {
        situation: 'FAILED',
        updated_at: now(),
      },
      true
    );

    return;
  }

  await createSentEmail(cond_uuid, emailId, {
    ...dbSave,
    situation: 'SENT',
  });

  await updateLotByID(
    cond_uuid,
    lot_uuid,
    {
      situation: 'PROCESSED',
      updated_at: now(),
    },
    true
  );
}

module.exports = { sendAndLog };
