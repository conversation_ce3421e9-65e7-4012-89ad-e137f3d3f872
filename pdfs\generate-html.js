const { storage, createPDF } = require('../lib');

module.exports.generateHtml = async (keys, templateId) => {
    try {
        const file = await storage.file('html-models/main.html');

        const [buffer] = await file.download();

        let template = buffer.toString();

        Object.keys(keys).forEach((key) => {
            template = template.replace(`{{ ${key} }}`, keys[key]);
        });

        const { doc, err } = await createPDF(template);
        if (err || !doc) {
            console.log('Deu erro gerando o pdf');
            console.log(doc);
            throw err;
        }

        if (typeof doc !== 'string') {
            console.log('Doc:', doc);
            throw 'Documento diferente de string';
        }

        return doc;
    } catch (e) {
        console.log(e);
        return '';
    }
};
