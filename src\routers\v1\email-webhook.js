const { emailWebhookEvents, updateEmail, now, admin } = require('../../../lib');

module.exports = async (req, res) => {
  try {
    const { body } = req;

    console.log(JSON.stringify(body));

    for (let i = 0; i < body.length; i++) {
      const data = body[0];
      let item = undefined;

      let ids = data.category.shift();
      ids = ids.split(':');

      if (data.event === emailWebhookEvents.open) {
        item = {
          cond_id: ids[0],
          email_id: ids[1],
          data: {
            opened: true,
            openedAt: admin.firestore.Timestamp.now(),
            updated_at: now(),
          },
        };
      } else if (data.event === emailWebhookEvents.clicked) {
        item = {
          cond_id: ids[0],
          email_id: ids[1],
          data: {
            clicked: true,
            clickedAt: admin.firestore.Timestamp.now(),
            updated_at: now(),
          },
        };
      } else {
        console.log(data);
        return;
      }

      const err = await updateEmail(
        item.cond_id,
        item.email_id,
        item.data
      );

      if (err) {
        console.log(
          `Erro ao tentar atualizar o email no banco. ID do cond: ${item.cond_id} - ID do email: ${item.email_id}`
        );
        console.log(err);
      }
    }

    res.status(200).send('Recebido com sucesso');
    return;
  } catch (e) {
    console.log('Erro ao receber um webhook da celcoin');
    console.log(e);
    res.status(500).send(
      'Problema no servidor interno. Tente novamente mais tarde!'
    );
  }
};
