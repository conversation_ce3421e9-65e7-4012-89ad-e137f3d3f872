const admin = require('firebase-admin');
const serviceAccount = require('../serviceAccountKey.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  storageBucket: 'condogaia-fe312.appspot.com',
});
const { sqsProcessType } = require('./types');

const firestore = admin.firestore();
const storage = admin.storage().bucket();
const notification = admin.messaging();

// =============== NOTIFICATION ===============
// Responsável por enviar notificações para os dispositivos inscritos em um tópico
const notify = async (title, body, topic) => {
  try {
    const message = {
      notification: {
        title,
        body,
      },
      topic,
    };

    const _ = await notification.send(message);

    return undefined;
  } catch (e) {
    return e;
  }
};

// ================= DATABASE =================
// ================== CREATE ==================
// Cria a transação no banco de dados
const createTransaction = async (cond_uuid, transaction_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('transactions')
      .doc(transaction_uuid)
      .set({ ...data });

    return undefined;
  } catch (e) {
    return e;
  }
};

// Salva o email enviado para o usuário no banco de dados
const createSentEmail = async (cond_id, email_id, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_id)
      .collection('emails-sent')
      .doc(email_id)
      .set(data);

    return undefined;
  } catch (e) {
    return e;
  }
};
// Cria a transação no banco de dados
const createLot = async (cond_uuid, lot_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('lots')
      .doc(lot_uuid)
      .set(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// Cria um acordo no banco de dados
const createAgreement = async (cond_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('agreements')
      .doc(data.uuid)
      .set(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// Cria uma unidade
const createUnit = async (cond_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('units')
      .doc(data.unitId)
      .set(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// Cria uma unidade
const createPayer = async (cond_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('payers')
      .doc(data.myId)
      .set(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// ================== GET ==================
// Busca dados de um lote pelo id
const getLotByID = async (cond_uuid, lot_uuid, type = sqsProcessType.single) => {
  try {
    const collectionMap = {
      [sqsProcessType.single]: 'lots',
      [sqsProcessType.monthly]: 'lots',
      [sqsProcessType.emails]: 'emails-lots',
      [sqsProcessType.agreement]: 'agreements',
    };

    const collectionName = collectionMap[type];

    if (!collectionName) {
      throw new Error('Tipo inválido');
    }

    const docRef = firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection(collectionName)
      .doc(lot_uuid);

    const docSnap = await docRef.get();
    console.log('exists:', docSnap.exists);
    console.log('data:', docSnap.data());

    if (!docSnap.exists || !docSnap.data() || Object.keys(docSnap.data()).length === 0) {
      return { lotData: null, gl_error: 'Documento não encontrado' };
    }

    return { lotData: docSnap.data() };
  } catch (e) {
    return { gl_error: e };
  }
};


// busca dados de um condominio pelo id
const getCondByID = async (cond_uuid) => {
  try {
    const cond = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .get();

    return { condData: cond.data() };
  } catch (e) {
    return { gc_error: e };
  }
};

// Retorna a quantidade de chaves de idempotencia encontradas no banco
const getIdempotencyKey = async (cond_id, idempotency_key) => {
  try {
    const lots = await firestore
      .collection('condominios')
      .doc(cond_id)
      .collection('lots')
      .where('idempotency_key', '==', idempotency_key)
      .get();

    return { itens: lots.docs.length };
  } catch (e) {
    return { err: e };
  }
};

// Retorna o total de unidades do condomínio que não tem imunidade de pagamento de taxas
const getTotalUnitsLenth = async (cond_uuid) => {
  try {
    const unit = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('units')
      .where('immunity', '!=', 'Total')
      .get();

    return { itens: unit.docs.length };
  } catch (e) {
    return { err: e };
  }
};

// Busca um pagador pelo id
const getPayerByID = async (cond_uuid, payer_uuid) => {
  try {
    const payer = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('payers')
      .doc(payer_uuid)
      .get();

    return { payerData: payer.data() };
  } catch (e) {
    return { gp_error: e };
  }
};

// Busca a composição de um pagador pelo id
const getPayerCompositionByID = async (cond_uuid, payer_uuid) => {
  try {
    const payer = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('payers')
      .doc(payer_uuid)
      .collection('compositions')
      .get();

    return { payerCompositions: payer };
  } catch (e) {
    return { gpc_error: e };
  }
};

// Busca um lote de email no banco para processar no sqs
const getEmailLotByID = async (cond_uuid, lot_uuid) => {
  try {
    const lot = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('emails-lots')
      .doc(lot_uuid)
      .get();

    return { lotData: lot.data() };
  } catch (e) {
    return { gl_error: e };
  }
};

// Retorna todos os pagadores
const getPayers = async (cond_uuid) => {
  try {
    const payers = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('payers')
      .get();

    return { payers };
  } catch (e) {
    return { p_error: e };
  }
};

// Busca os usuários no banco
const getUsers = async (where = undefined) => {
  try {
    let users = undefined;
    if (where) {
      users = await firestore
        .collection('users')
        .where('role', '==', where)
        .get();
    } else {
      users = await firestore.collection('users').get();
    }

    return { users };
  } catch (e) {
    return { u_error: e };
  }
};

// Busca uma transação pelo id
const getTransactionByID = async (cond_uuid, transaction_uuid) => {
  try {
    const transaction = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('transactions')
      .doc(transaction_uuid)
      .get();

    return { transactionData: transaction.data() };
  } catch (e) {
    return { gt_error: e };
  }
};

// Retorna todas as unidades do condomínio
const getUnits = async (cond_uuid) => {
  try {
    const units = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('units')
      .get();

    return { units: units.docs };
  } catch (e) {
    return { gu_error: e };
  }
};


// Busca um pagador pelo unitId
const getPayerByUnitId = async (cond_uuid, unit_id) => {
  try {
    const payersSnapshot = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('payers')
      .where('unitId', '==', unit_id)
      .limit(1)
      .get();

    if (payersSnapshot.empty) {
      return { payerData: null };
    }
    const payerDoc = payersSnapshot.docs[0];
    return { payerData: { id: payerDoc.id, ...payerDoc.data() } };
  } catch (e) {
    return { fp_error: e };
  }
};

// Busca uma unidade pelo nome e bloco
const getUnitByBlockAndUnitName = async (cond_uuid, block, unitName) => {
  try {
    const unitsSnapshot = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('units')
      .where('block', '==', block)
      .where('unit', '==', unitName)
      .limit(1)
      .get();

    if (unitsSnapshot.empty) {
      return { unitData: null };
    }
    const unitDoc = unitsSnapshot.docs[0];
    return { unitData: { id: unitDoc.id, ...unitDoc.data() } };
  } catch (e) {
    return { gu_error: e };
  }
};

// Retorna todos os pagadores
const getCondBankAccounts = async (cond_uuid, all = true) => {
  try {
    if (!all) {
      const bankAccounts = await firestore
        .collection('condominios')
        .doc(cond_uuid)
        .collection('bank_accounts')
        .where('main', '==', true)
        .get();

      return { bankAccounts };
    } else {
      const bankAccounts = await firestore
        .collection('condominios')
        .doc(cond_uuid)
        .collection('bank_accounts')
        .get();

      return { bankAccounts };
    }
  } catch (e) {
    return { bc_error: e };
  }
};

// Retorna um condomínio pelo confirm hash do webhook
const getCondIDByConfirmHash = async (confirmHash) => {
  try {
    const cond = await firestore
      .collection('condominios')
      .where('confirmHashWebhook', '==', confirmHash)
      .get();

    if (cond.docs.length === 0) {
      throw 'Condominio não encontrado ';
    }

    const item = cond.docs[0];

    return { cond_id: item.id, condRegister: item.data().condRegister };
  } catch (e) {
    return { gch_error: e };
  }
};

// Busca um acordo no banco
const getAgreementByID = async (cond_uuid, agreement_uuid) => {
  try {
    const agreement = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('agreements')
      .doc(agreement_uuid)
      .get();

    return { agreementData: agreement.data() };
  } catch (e) {
    return { ga_error: e };
  }
};

// scheduler
// busca todos os condomínios
const getConds = async () => {
  try {
    const conds = await firestore.collection('condominios').get();

    return { conds: conds.docs };
  } catch (e) {
    return { gc_error: e };
  }
};

// busca todas as despesas recorrentes
const getInfinityExpanses = async (cond_uuid) => {
  try {
    const ie = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('infinite-expenses')
      .get();

    return { ie: ie.docs };
  } catch (e) {
    return { gie_error: e };
  }
};

// Busca todas as transações que tenham o status igual ao informado
const getTransactionsBySituation = async (cond_uuid, situation) => {
  try {
    const transaction = await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('transactions')
      .where('situation', '==', situation)
      .get();

    return { transactions: transaction.docs };
  } catch (e) {
    return { gt_error: e };
  }
};

// ================== UPDATE ==================
// Atualiza o lote no banco
const updateLotByID = async (cond_id, lot_id, data, type = undefined) => {
  try {
    if (type) {
      await firestore
        .collection('condominios')
        .doc(cond_id)
        .collection('emails-lots')
        .doc(lot_id)
        .update(data);

      return undefined;
    } else {
      await firestore
        .collection('condominios')
        .doc(cond_id)
        .collection('lots')
        .doc(lot_id)
        .update(data);

      return undefined;
    }
  } catch (e) {
    return e;
  }
};

// Atualiza a transação no banco
const updateTransaction = async (cond_id, transaction_id, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_id)
      .collection('transactions')
      .doc(transaction_id)
      .update(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// Atualiza o nosso numero no banco
const updateOurNumber = async (cond_id, number) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_id)
      .update({ our_number: number });

    return undefined;
  } catch (e) {
    return e;
  }
};

// Atualiza o acordo no banco
const updateAgreement = async (cond_id, agreement_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_id)
      .collection('agreements')
      .doc(agreement_uuid)
      .update(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// Atualiza o acordo no banco
const updateEmail = async (cond_id, email_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_id)
      .collection('emails-sent')
      .doc(email_uuid)
      .update(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// Atualiza o status de documento do condomínio da celcoin no banco
const updateAccountDocument = async (cond_id, data) => {
  try {
    await firestore.collection('condominios').doc(cond_id).update(data);

    return undefined;
  } catch (e) {
    return e;
  }
};


// Atualiza uma unidade
const updateUnit = async (cond_uuid, unit_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('units')
      .doc(unit_uuid)
      .update(data);

    return undefined;
  } catch (e) {
    return e;
  }
};

// Atualiza um pagador
const updatePayer = async (cond_uuid, payer_uuid, data) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_uuid)
      .collection('payers')
      .doc(payer_uuid)
      .update(data);
    return undefined;
  } catch (e) {
    return e;
  }
};

// ================== DELETE ==================
const deletePayerComposition = async (cond_id, payer_id, composition_id) => {
  try {
    await firestore
      .collection('condominios')
      .doc(cond_id)
      .collection('payers')
      .doc(payer_id)
      .collection('compositions')
      .doc(composition_id)
      .delete();

    return undefined;
  } catch (e) {
    return e;
  }
};

module.exports = {
  admin,
  firestore,
  storage,
  // notification
  notify,
  // DB
  // create
  createTransaction,
  createSentEmail,
  createLot,
  createAgreement,
  createUnit,
  createPayer,
  updateUnit,
  updatePayer,
  // get
  getLotByID,
  getCondByID,
  getIdempotencyKey,
  getTotalUnitsLenth,
  getPayerByID,
  getPayers,
  getPayerCompositionByID,
  getEmailLotByID,
  getUsers,
  getTransactionByID,
  getUnits,
  getCondBankAccounts,
  getCondIDByConfirmHash,
  getConds,
  getInfinityExpanses,
  getTransactionsBySituation,
  getAgreementByID,
  getUnitByBlockAndUnitName,
  getPayerByUnitId,
  // update
  updateLotByID,
  updateTransaction,
  updateOurNumber,
  updateAgreement,
  updateEmail,
  updateAccountDocument,
  updateUnit,
  updatePayer,
  // delete
  deletePayerComposition,
};
