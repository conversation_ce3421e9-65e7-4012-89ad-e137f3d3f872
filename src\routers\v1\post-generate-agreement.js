const { v4: uuidv4 } = require('uuid');
const {
    sendMessage,
    now,
    createAgreement,
    getPayerByID,
    sqsProcessType,
    agreementSituation,
} = require('../../../lib');

// Função responsável pelo processamento de acordo
module.exports = async (req) => {
    try {
        console.log('Api de acordo');
        const { body, headers } = req;
        const cid = headers['x-cid'];

        if (!validate(body)) {
            return {
                status: 400,
                code: 'MS00023',
                result: 'O body da requisição é inválido',
            };
        }

        let err = undefined;

        const { payerData, gp_error } = await getPayerByID(cid, body.payerId);
        if (gp_error) {
            console.log(gp_error);
            return {
                status: 500,
                code: 'MS00028',
                result: 'Erro ao tentar buscar os dados do pagador, tente novamente mais tarde',
            };
        }

        const ammount = calculateAmmount(body);

        const agreeUUID = uuidv4();
        err = await createAgreement(cid, {
            ...body,
            uuid: agreeUUID,
            totalAmmount: ammount,
            unitName: payerData.unitName,
            unitBlock: payerData.unitBlock,
            situation: agreementSituation.processing,
            created_at: now(),
            updated_at: now(),
        });

        if (err) {
            console.log(err);
            return {
                status: 500,
                code: 'MS00027',
                result: 'Erro ao tentar criar o acordo no banco de dados',
            };
        }

        err = await sendMessage(agreeUUID, cid, sqsProcessType.agreement, {});

        if (err) {
            console.log(err);
            return {
                status: 500,
                code: 'MS00021',
                result: 'Ocorreu um erro ao tentar mandar os dados pra fila de processamento',
            };
        }

        return {
            status: 200,
            result: 'Acordo criado e o boletos cancelados com sucesso',
        };
    } catch (e) {
        console.log(e);
        return {
            status: 500,
            code: 'MS00022',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};

const validate = (body) => {
    return true;
};

const calculateAmmount = (body) => {
    let ammount = body.ammount;
    ammount = ammount - body.inflow; // valor de entrada
    ammount = ammount + body.expenses; // despesas adicionais
    ammount = ammount + body.index; // indice igpm

    if (ammount <= 500) {
        return body.ammount;
    }

    return ammount;
};
