const { v4: uuidv4 } = require('uuid');
const {
  createUnit,
  createPayer,
  unitSheetTemplateKeys,
  xslxImportTypes,
  isEmpty,
  getUnitByBlockAndUnitName,
  getPayerByUnitId,
  updateUnit,
  updatePayer,
  createAgreement,
  getPayerByID,
  sendMessage,
  sqsProcessType,
  agreementSituation,
  now,
  validateDate,
  onlyNumbers,
} = require('../../lib');

const validateRequest = (body, headers) => {
  const cid = headers['x-cid'];
  if (!cid) {
    return { error: 'O id do condomínio é obrigatório', code: 'MS00081' };
  }
  if (!body || !body.url || String(body.url).trim().length === 0) {
    return { error: 'O campo "url" é obrigatório e não pode ser vazio', code: 'MS00082' };
  }
  if (isEmpty(body.type) || !xslxImportTypes[body.type]) {
    return { error: 'O campo "type" está inválido', code: 'MS00083' };
  }
  return { cid, url: body.url, type: body.type };
};

const prepareSheetUrl = (originalUrl) => {
  // Remove tudo após o ?
  const baseUrl = originalUrl.split('?')[0];

  if (baseUrl.endsWith('/edit')) {
    return baseUrl.replace('/edit', '/export?format=xlsx');
  }

  return baseUrl.endsWith('/')
    ? `${baseUrl}export?format=xlsx`
    : `${baseUrl}/export?format=xlsx`;
};


// =========== UNIT IMPORT HELPERS ===========
const createUnitPayload = (rowData, unitUUID, ownerUUID, payerUUID, cid) => {
  const payload = {
    area: 0, fraction: 0, lawsuit: false, mergeBills: false, condIds: { [cid]: [] }, obs: '',
    ownerOrTenant: 'Proprietário', ownerFile: '', ownerFileId: '', ownerId: ownerUUID,
    ownerImage: '', ownerImageId: '', ownerTel: '', owners: [], payerId: payerUUID, photo: '',
    postOffice: false, qrCode: '', receiveEmail: true, residents: [], tenants: [],
    unitId: unitUUID, unit: '', block: '', differentMaturity: '', payDifferentAmmount: 0,
    immunity: 'Nenhum', ownerName: '', ownerRegister: '', ownerEmail: '', ownerCel: '',
    ownerAdress: '', ownerHouseNumber: '', ownerNeighborhood: '', ownerCity: '',
    ownerState: '', ownerZipCode: '', vehicles: [], wifeOrHusband: '',
  };

  for (const sheetKey in unitSheetTemplateKeys) {
    if (rowData.hasOwnProperty(sheetKey)) {
      const mappedKey = unitSheetTemplateKeys[sheetKey];
      let value = rowData[sheetKey];

      if (mappedKey === 'payDifferentAmmount') {
        const numericValue = parseFloat(String(value).replace(',', '.'));
        payload[mappedKey] = !isNaN(numericValue) ? Math.round(numericValue * 100) : 0;
      } else if (mappedKey === 'ownerRegister' || mappedKey === 'differentMaturity') {
        payload[mappedKey] = onlyNumbers(String(value));
      } else if (typeof value === 'number') {
        payload[mappedKey] = String(value);
      } else {
        payload[mappedKey] = value !== undefined && value !== null ? String(value) : (payload[mappedKey] || '');
      }
    }
  }
  payload.unit = payload.unit || '';
  payload.block = payload.block || '';
  return payload;
};

const createPayerPayload = (rowData, payerUUID, unitUUID) => {
  const getValue = (sheetKey, defaultValue = '') => {
    const value = rowData[sheetKey];
    return value !== undefined && value !== null ? String(value) : defaultValue;
  };
  const getNumericOnlyValue = (sheetKey, defaultValue = '') => {
    const value = rowData[sheetKey];
    return value !== undefined && value !== null ? onlyNumbers(String(value)) : defaultValue;
  };

  // Helper to find the original sheet key (Excel header) from a mapped value in unitSheetTemplateKeys
  const findSheetKeyByMappedValue = (mappedValue) => Object.keys(unitSheetTemplateKeys).find(k => unitSheetTemplateKeys[k] === mappedValue);

  return {
    Address: {
      city: getValue(findSheetKeyByMappedValue('ownerCity')),
      complement: '',
      neighborhood: getValue(findSheetKeyByMappedValue('ownerNeighborhood')),
      number: getValue(findSheetKeyByMappedValue('ownerHouseNumber')),
      state: getValue(findSheetKeyByMappedValue('ownerState')),
      street: getValue(findSheetKeyByMappedValue('ownerAdress')),
      zipCode: getNumericOnlyValue(findSheetKeyByMappedValue('ownerZipCode')),
    },
    document: getNumericOnlyValue(findSheetKeyByMappedValue('ownerRegister')),
    myId: payerUUID,
    name: getValue(findSheetKeyByMappedValue('ownerName')),
    emails: [getValue(findSheetKeyByMappedValue('ownerEmail'))].filter(e => e && e.trim() !== ''),
    phones: [getNumericOnlyValue(findSheetKeyByMappedValue('ownerCel'))].filter(p => p && p.trim() !== ''),
    unitId: unitUUID,
    unitBlock: getValue(findSheetKeyByMappedValue('block')),
    unitName: getValue(findSheetKeyByMappedValue('unit')),
  };
};

const processUnitRow = async (rowData, cid, rowIndex) => {
  for (const key in rowData) {
    if (rowData.hasOwnProperty(key) && key.startsWith('__EMPTY')) {
      delete rowData[key]; // Remove colunas vazias geradas pelo xlsx
    }
  }

  const unitName = String(rowData['nome_unidade'] || '');
  const blockName = String(rowData['bloco_unidade'] || '');

  // Buscar unidade existente pelo unit + block + cid
  const { unitData: existingUnit, gu_error } = await getUnitByBlockAndUnitName(cid, blockName, unitName);

  let unitUUID, ownerUUID, payerUUID;

  if (existingUnit) {
    unitUUID = existingUnit.id;
    ownerUUID = existingUnit.ownerId || uuidv4();
    payerUUID = existingUnit.payerId || uuidv4();

    // Cria payload atualizado para a unidade, mantendo unitUUID e ids atuais
    const payloadUnit = createUnitPayload(rowData, unitUUID, ownerUUID, payerUUID, cid);

    try {
      // Atualiza unidade ao invés de criar
      const unitError = await updateUnit(cid, unitUUID, payloadUnit);
      if (unitError) {
        console.error(`Erro ao atualizar unidade na linha ${rowIndex + 1} (Unidade: ${unitName}, Bloco: ${blockName}):`, unitError);
        return { error: unitError, type: 'update-unit', unit: unitName, block: blockName, ownerDocument: payloadUnit.ownerRegister };
      }

      // Verificar pagador existente para esta unidade
      const { payerData: existingPayer, fp_error } = await getPayerByUnitId(cid, unitUUID);
      const payloadPayer = createPayerPayload(rowData, payerUUID, unitUUID);

      if (existingPayer) {
        const payerError = await updatePayer(cid, existingPayer.myId, payloadPayer);
        if (payerError) {
          console.error(`Erro ao atualizar pagador na linha ${rowIndex + 1} (Unidade: ${unitName}, Bloco: ${blockName}):`, payerError);
          return { error: payerError, type: 'update-payer', unit: unitName, block: blockName, ownerDocument: payloadUnit.ownerRegister };
        }
      } else {
        const payerError = await createPayer(cid, payloadPayer);
        if (payerError) {
          console.error(`Erro ao criar pagador na linha ${rowIndex + 1} (Unidade: ${unitName}, Bloco: ${blockName}):`, payerError);
          return { error: payerError, type: 'create-payer', unit: unitName, block: blockName, ownerDocument: payloadUnit.ownerRegister };
        }
      }

      return { success: true, unit: unitName, block: blockName };

    } catch (e) {
      console.error(`Exceção ao processar linha ${rowIndex + 1} (Unidade: ${unitName}, Bloco: ${blockName}):`, e);
      return { error: e, type: 'processing-exception', unit: unitName, block: blockName, ownerDocument: payloadUnit.ownerRegister };
    }

  } else {
    // Unidade não existe: criar novos UUIDs e criar registros novos
    unitUUID = uuidv4();
    ownerUUID = uuidv4();
    payerUUID = uuidv4();

    const payloadUnit = createUnitPayload(rowData, unitUUID, ownerUUID, payerUUID, cid);
    const payloadPayer = createPayerPayload(rowData, payerUUID, unitUUID);

    try {
      const unitError = await createUnit(cid, payloadUnit);
      if (unitError) {
        console.error(`Erro ao criar unidade para linha ${rowIndex + 1} (Unidade: ${unitName}, Bloco: ${blockName}):`, unitError);
        return { error: unitError, type: 'create-unit', unit: unitName, block: blockName, ownerDocument: payloadUnit.ownerRegister };
      }

      const payerError = await createPayer(cid, payloadPayer);
      if (payerError) {
        console.error(`Erro ao criar pagador para linha ${rowIndex + 1} (Unidade: ${unitName}, Bloco: ${blockName}):`, payerError);
        return { error: payerError, type: 'create-payer', unit: unitName, block: blockName, ownerDocument: payloadUnit.ownerRegister };
      }

      return { success: true, unit: unitName, block: blockName };
    } catch (e) {
      console.error(`Exceção ao processar linha ${rowIndex + 1} (Unidade: ${unitName}, Bloco: ${blockName}):`, e);
      return { error: e, type: 'processing-exception', unit: unitName, block: blockName, ownerDocument: payloadUnit.ownerRegister };
    }
  }
};

// =========== AGREEMENT IMPORT HELPERS ===========

const calculateAgreementTotalAmmount = (data) => {
  // data contains parsed values: ammount, inflow, expenses, indexValue
  let total = parseFloat(data.ammount) || 0;
  total -= (parseFloat(data.inflow) || 0);
  total += (parseFloat(data.expenses) || 0);
  total += (parseFloat(data.indexValue) || 0);

  // Replicating the original logic from post-generate-agreement.js
  // This business rule (returning original amount if calculated is <= 500) should be confirmed.
  if (total <= 500 && (parseFloat(data.ammount) > 0)) {
    return parseFloat(data.ammount);
  }
  return total;
};

const processAgreementRow = async (rowData, cid, rowIndex, agreementSheetTemplateKeys) => {
  const parsedData = {};
  const validationErrors = [];

  for (const excelHeader in agreementSheetTemplateKeys) {
    const internalKey = agreementSheetTemplateKeys[excelHeader];
    let value = rowData[excelHeader];

    if (value !== undefined && value !== null) {
      value = String(value).trim();
      switch (internalKey) {
        case 'ammount': case 'inflow': case 'expenses': case 'indexValue': case 'fine': case 'fees':
          parsedData[internalKey] = parseFloat(String(value).replace(',', '.')) || 0; // Handle comma decimal
          break;
        case 'parcels':
          parsedData[internalKey] = parseInt(value, 10);
          if (isNaN(parsedData[internalKey])) parsedData[internalKey] = 0;
          break;
        case 'feesAndFine': case 'sendEmail': case 'isRegistered':
          parsedData[internalKey] = value.toUpperCase() === 'SIM';
          break;
        case 'chargeIds':
          parsedData[internalKey] = value.split(',').map(id => id.trim()).filter(id => id);
          break;
        default:
          parsedData[internalKey] = value;
      }
    } else {
      // Set defaults for optional fields if not present in sheet
      switch (internalKey) {
        case 'inflow': case 'expenses': case 'indexValue': case 'fine': case 'fees': parsedData[internalKey] = 0; break;
        case 'feesAndFine': case 'sendEmail': case 'isRegistered': parsedData[internalKey] = true; break;
        case 'issuer': parsedData[internalKey] = 'condominio'; break;
      }
    }
  }

  // Validation
  if (!parsedData.payerId) validationErrors.push(`Coluna '${Object.keys(agreementSheetTemplateKeys).find(k => agreementSheetTemplateKeys[k] === 'payerId')}' é obrigatória.`);
  if (!parsedData.chargeIds || parsedData.chargeIds.length === 0) validationErrors.push(`Coluna '${Object.keys(agreementSheetTemplateKeys).find(k => agreementSheetTemplateKeys[k] === 'chargeIds')}' é obrigatória e deve conter IDs válidos.`);
  if (typeof parsedData.ammount !== 'number') validationErrors.push(`Coluna '${Object.keys(agreementSheetTemplateKeys).find(k => agreementSheetTemplateKeys[k] === 'ammount')}' é obrigatória e deve ser numérica.`);
  if (!parsedData.parcels || parsedData.parcels <= 0) validationErrors.push(`Coluna '${Object.keys(agreementSheetTemplateKeys).find(k => agreementSheetTemplateKeys[k] === 'parcels')}' é obrigatória e deve ser um inteiro positivo.`);
  if (!parsedData.maturity || !validateDate(parsedData.maturity)) validationErrors.push(`Coluna '${Object.keys(agreementSheetTemplateKeys).find(k => agreementSheetTemplateKeys[k] === 'maturity')}' é obrigatória e deve ser uma data válida (AAAA-MM-DD).`);
  if (parsedData.inflowDate && !validateDate(parsedData.inflowDate)) validationErrors.push(`Coluna '${Object.keys(agreementSheetTemplateKeys).find(k => agreementSheetTemplateKeys[k] === 'inflowDate')}' deve ser uma data válida (AAAA-MM-DD) se preenchida.`);
  if (parsedData.monthCharge && !validateDate(parsedData.monthCharge)) validationErrors.push(`Coluna '${Object.keys(agreementSheetTemplateKeys).find(k => agreementSheetTemplateKeys[k] === 'monthCharge')}' deve ser uma data válida (AAAA-MM-DD) se preenchida.`);


  if (validationErrors.length > 0) {
    return { error: `Linha ${rowIndex + 1}: ${validationErrors.join('; ')}`, type: 'validation-agreement' };
  }

  const { payerData, gp_error } = await getPayerByID(cid, parsedData.payerId);
  if (gp_error || !payerData) {
    return { error: `Linha ${rowIndex + 1}: Pagador com ID '${parsedData.payerId}' não encontrado ou erro ao buscar.`, type: 'fetch-payer-agreement' };
  }

  const totalAmmountToParcel = calculateAgreementTotalAmmount(parsedData);
  if (totalAmmountToParcel <= 0 && parsedData.parcels > 0) {
    return { error: `Linha ${rowIndex + 1}: Valor total a parcelar (${totalAmmountToParcel.toFixed(2)}) é zero ou negativo. Verifique os valores.`, type: 'calculation-agreement' };
  }

  const agreeUUID = uuidv4();
  const finalAgreementData = {
    uuid: agreeUUID,
    payerId: parsedData.payerId,
    chargeIds: parsedData.chargeIds,
    ammount: Math.round(parsedData.ammount * 100), // Store in cents
    inflow: Math.round(parsedData.inflow * 100),
    inflowDate: parsedData.inflowDate,
    expenses: Math.round(parsedData.expenses * 100),
    index: Math.round(parsedData.indexValue * 100), // 'index' is the field name in DB for indexValue
    parcels: parsedData.parcels,
    maturity: parsedData.maturity,
    fine: parsedData.fine, // Assuming fine/fees are stored as percentages
    fees: parsedData.fees,
    feesAndFine: parsedData.feesAndFine,
    sendEmail: parsedData.sendEmail,
    issuer: parsedData.issuer,
    monthCharge: parsedData.monthCharge,
    isRegistered: parsedData.isRegistered,
    totalAmmount: Math.round(totalAmmountToParcel * 100), // This is the amount to be parcelled, in cents
    unitName: payerData.unitName,
    unitBlock: payerData.unitBlock,
    situation: agreementSituation.processing,
    created_at: now(),
    updated_at: now(),
  };

  try {
    const createError = await createAgreement(cid, finalAgreementData);
    if (createError) {
      return { error: `Linha ${rowIndex + 1}: Erro ao criar acordo BD (${createError.message || createError})`, type: 'create-agreement-db' };
    }

    const sqsError = await sendMessage(agreeUUID, cid, sqsProcessType.agreement, {});
    if (sqsError) {
      // Consider rollback or marking for retry if SQS fails
      return { error: `Linha ${rowIndex + 1}: Erro ao enviar acordo para SQS (${sqsError.message || sqsError})`, type: 'sqs-agreement' };
    }
    return { success: true, agreementUUID: agreeUUID, payerId: parsedData.payerId, unit: `${payerData.unitBlock}/${payerData.unitName}` };
  } catch (e) {
    return { error: `Linha ${rowIndex + 1}: Exceção (${e.message || e})`, type: 'processing-exception-agreement' };
  }
};

module.exports = {
  validateRequest,
  prepareSheetUrl,
  processUnitRow,
  processAgreementRow,
};