const { getLotByID } = require('../../../lib/index');

/*
 * Default module exports
 * lotProcess - Função pra retornar a quantidade de itens processados do lote
 */
module.exports = async (req) => {
    try {
        const { cond_uuid, lot_uuid } = req.query;

        if (
            !cond_uuid ||
            !lot_uuid ||
            cond_uuid.length === 0 ||
            lot_uuid.length === 0
        ) {
            return {
                status: 400,
                code: 'MS0011',
                result: 'Os parâmetros cond_uuid e lot_uuid são brigatórios',
            };
        }

        const { lotData, gl_error } = await getLotByID(cond_uuid, lot_uuid);

        if (gl_error) {
            return {
                status: 404,
                code: 'MS0012',
                result: 'Lote não encotrado',
            };
        }

        return {
            status: 200,
            result: lotData.count,
        };
    } catch (e) {
        console.error('Caiu no catch');
        console.error(e);
        return {
            status: 500,
            code: 'MS0010',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};
