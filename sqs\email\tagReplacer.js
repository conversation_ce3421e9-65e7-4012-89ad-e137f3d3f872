function replaceTags(text, unitData, condData, isOwner) {
  const name = isOwner ? unitData.ownerName : unitData.tenantName;
  const address = isOwner
    ? `${unitData.ownerAdress}, ${unitData.ownerHouseNumber}`
    : `${unitData.tenantAdress}, ${unitData.tenantHouseNumber}`;
  const phone = isOwner ? unitData.ownerCel : unitData.tenantCel;

  return text
    .replaceAll('#nome', name)
    .replaceAll('#unidade', unitData.unit)
    .replaceAll('#condominio', condData.condName)
    .replaceAll('#bloco', unitData.block)
    .replaceAll('#boleto_aberto', 'Boleto aberto')
    .replaceAll('#endereco', address)
    .replaceAll('#tel', phone)
    .replaceAll('#boleto_acordo', 'Boleto acordo');
}

module.exports = { replaceTags };
