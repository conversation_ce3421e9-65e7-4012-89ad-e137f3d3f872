const express = require('express');
const { responseWith, responseWithWebhook, auth } = require('../middlewares');
const {
    health,
    generateBankslip,
    singleBankslip,
    getLotProcess,
    generateEmails,
    generateAgreement,
    celcoinWebhook,
    emailWebhook,
    deleteBankslip,
    receiveBankslip,
    notify,
    deleteAgreement,
    viewTemplates,
    postImportXslx,
} = require('./v1/index');

const router = express.Router();

// NO AUTH ROUTES
// health - Route to check server health (for kubernate use)
router.get('/health', (req, res, next) => responseWith(req, res, next, health));

// notify - Rota pra receber e notificar os usuários
router.post('/notify', (req, res, next) =>
    responseWith(req, res, next, notify)
);

// AUTH ROUTES
// generate bankslip - Router to create bankslip lot and order de sqs queue to process all the bankslips
router.post(
    '/generate-bankslip',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, generateBankslip)
);

// single-bankslip - Router to create a single bill lots
router.post(
    '/single-bankslip',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, singleBankslip)
);

// single-bankslip - Router to create a single bill lots
router.post(
    '/generate-emails',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, generateEmails)
);

// generate-agreement - Router to create a agreement bankslip
router.post(
    '/generate-agreement',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, generateAgreement)
);

// lot-process - Router to get processing lot status
router.get(
    '/lot-process',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, getLotProcess)
);

// cancel-bankslip - Rota pra cancelar uma lista de boletos
router.post(
    '/cancel-bankslip',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, deleteBankslip)
);

// receive-bankslip - Rota pra receber e atualizar como pago uma lista de boletos
router.post(
    '/receive-bankslip',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, receiveBankslip)
);

// cancel-agreement - Rota para cancelar um acordo
router.post(
    '/cancel-agreement',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, deleteAgreement)
);

// view-templates - Rota para visualizar o template dos boletos
router.post(
    '/view-templates',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, viewTemplates)
);

// import - Rota para fazer a importação de uma planilha xslx
router.post(
    '/import',
    (req, res, next) => auth(req, res, next),
    (req, res, next) => responseWith(req, res, next, postImportXslx)
);

// ============ webhooks ============
// webhook de atualização da celcoin
router.post('/webhook', (req, res, next) =>
    responseWithWebhook(req, res, next, celcoinWebhook)
);

// webhook da sendgrid
router.post('/email-webhook', (req, res, next) =>
    responseWithWebhook(req, res, next, emailWebhook)
);

module.exports = router;
