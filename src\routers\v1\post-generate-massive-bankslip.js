const {
    now,
    validateDate,
    validIdempotencyKey,
    sendMessage,
    getPayers,
    createLot,
    lotSituation,
    bankslipTypes,
    sqsProcessType,
} = require('../../../lib/index');

/*
 * Default module exports
 * Generate bankslip - Reposnsável por criar os lotes que serão processados pelo sqs
 */
module.exports = async (req) => {
    try {
        console.log('Rodando api de boleto em massa');
        const { headers, body } = req;
        const cid = headers['x-cid'];

        const validBody = await validate(body, cid);

        if (!validBody.val) {
            console.log(`Invalid body request: ${validBody.reason}`);
            return {
                status: 400,
                code: 'MS0008',
                result: `Invalid body request: ${validBody.reason}`,
            };
        }

        const { payers, p_error } = await getPayers(cid);

        if (p_error) {
            return {
                status: 500,
                code: 'MS0009',
                result: 'Erro ao tentar buscar os pagadores no banco, tente novamente mais tarde',
            };
        }

        if (payers.docs.length === 0) {
            console.log('Não há dados para serem processados!');
            return {
                status: 204,
                result: 'Não há dados para serem processados',
            };
        }

        const lotUUID = body.idempotency_key;

        const payload = {
            ...body,
            emissionType: bankslipTypes.monthly,
            situation: lotSituation.pending,
            sendEmail: body.sendEmail,
            itens: [],
            created_at: now(),
        };

        payers.docs.forEach((item) => {
            payload.itens.push(item.id);
        });

        const rest = payers.docs.length % 10; // resto da divisão por 10
        const total = (payers.docs.length - rest) / 10; // total de itens sem o resto
        const val = rest > 0 ? total + 1 : total; // esse é o numero de lots que serão enviados pro sqs
        let offset = 0; // valor do offset do slice
        let limit = val === 1 ? rest : 10; // valor limite do slice

        payload.count = {
            total: payers.docs.length,
            processed: 0,
        };

        let err = await createLot(cid, lotUUID, payload);
        if (err) {
            console.log(err);
            return {
                status: 500,
                code: 'MS0010',
                result: 'Erro ao tentar criar o lote no banco, tente novamente mais tarde',
            };
        }

        for (let i = 1; i <= val; i++) {
            err = await sendMessage(lotUUID, cid, sqsProcessType.monthly, {
                offset,
                limit,
            });

            if (err) {
                console.log(err);
                return {
                    status: 500,
                    code: 'MS00011',
                    result: 'Ocorreu um erro ao tentar mandar os dados pra fila de processamento',
                };
            }

            offset = offset + 10;
            limit = limit + 10;
        }

        return {
            status: 201,
            result: {
                lot_uuid: lotUUID,
                message: 'Os boletos estão em fila de processamento',
            },
        };
    } catch (e) {
        console.error(e);
        return {
            status: 500,
            code: 'MS0002',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};

const validate = async (body, cid) => {
    if (!body.maturity || !body.idempotency_key) {
        return { val: false, reason: 'Conteúdo do body diferente do esperado' };
    }

    const validIDPK = await validIdempotencyKey(cid, body.idempotency_key);

    if (!validIDPK) {
        return {
            val: false,
            reason: 'A chave de idempotência não é válida ou já está sendo processada',
        };
    }

    return { val: validateDate(), reason: undefined };
};
