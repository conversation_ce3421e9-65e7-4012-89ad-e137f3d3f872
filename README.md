# Condogaia Scheduler

Este é o backend responsável por processar tarefas agendadas e filas SQS do Condogaia.

## 📁 Estrutura do Projeto

```
project/
├── sqs/
│   └── index.js         # Job para leitura e processamento das filas SQS
├── src/
│   └── index.js         # Lógica principal do scheduler
└── index.js             # Entry point principal
```

---

## 🚀 Como rodar o projeto na AWS EC2

### Pré-requisitos (já instalados na EC2):
- Docker
- Docker Compose

### Passos para subir o projeto:

1. **Acesse sua instância EC2:**
   ```bash
   ssh ec2-user@<seu-endereço-ip>
   ```

2. **Vá até o diretório do projeto:**
   ```bash
   cd dev/condogaia-scheduler
   ```

3. **Suba os containers com Docker Compose:**
   ```bash
   docker-compose up --build
   ```

   > O parâmetro `--build` garante que as imagens sejam reconstruídas se houver alterações.

---

## 🧩 Serviços

O `docker-compose` sobe dois serviços:

1. **scheduler**: Lê tarefas e executa lógicas recorrentes (pasta `src/`).
2. **sqs-consumer**: Lê e processa mensagens da fila SQS (pasta `sqs/`).

---

## 📝 Observações

- Sempre que reiniciar a instância EC2, repita os passos de acesso e execução acima.
- Logs dos serviços aparecem diretamente no terminal após rodar o `docker-compose up`.
