const health = require('./health');
const generateBankslip = require('./post-generate-massive-bankslip');
const singleBankslip = require('./post-single-bankslip');
const getLotProcess = require('./get-lot-process');
const generateEmails = require('./post-generate-massive-email');
const generateAgreement = require('./post-generate-agreement');
const deleteBankslip = require('./cancel-bankslip');
const receiveBankslip = require('./receive-bankslip');
const notify = require('./post-notify');
const deleteAgreement = require('./delete-agreement');
const viewTemplates = require('./post-view-template');
const postImportXslx = require('./post-import-xslsx');
const downloadTemplates = require('./download-templates');

//webhooks
const celcoinWebhook = require('./celcoin-webhook');
const emailWebhook = require('./email-webhook');

module.exports = {
    health,
    generateBankslip,
    singleBankslip,
    getLotProcess,
    generateEmails,
    generateAgreement,
    celcoinWebhook,
    emailWebhook,
    deleteBankslip,
    receiveBankslip,
    notify,
    deleteAgreement,
    viewTemplates,
    postImportXslx,
    downloadTemplates,
};
