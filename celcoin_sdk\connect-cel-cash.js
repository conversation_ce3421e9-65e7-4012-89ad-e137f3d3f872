const axios = require('axios');
const { allowedMethods } = require('./types');
const baseURL = process.env.CELCASH_BASE_URL;

// Retorna um header pronto pra enviar requisição pra celcoin
const celcashHeader = async (galaxId, galaxyHash) => {
    try {
        const payload = {
            grant_type: 'authorization_code',
            scope: 'company.read customers.read customers.write plans.read plans.write transactions.read transactions.write webhooks.write balance.read balance.write cards.read cards.write card-brands.read subscriptions.read subscriptions.write charges.read charges.write boletos.read',
        };

        const encoded64 = btoa(`${galaxId}:${galaxyHash}`);

        const headerAuth = {
            Authorization: `Basic ${encoded64}`,
            'Content-Type': 'application/json',
        };

        const resp = await axios({
            method: allowedMethods.post,
            url: `${baseURL}/token`,
            data: payload,
            headers: headerAuth,
        });

        if (resp.status != 200) {
            console.log(`Celcoin Auth error: ${resp.response.data}`);
            throw { ...resp.response };
        }

        const header = {
            Authorization: `Bearer ${resp.data.access_token}`,
            'Content-Type': 'application/json',
        };

        return { header };
    } catch (e) {
        return { ch_error: e };
    }
};

module.exports = { celcashHeader };
