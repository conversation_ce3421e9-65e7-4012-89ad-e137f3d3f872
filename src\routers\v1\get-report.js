const { isEmpty } = require('../../../lib');

module.exports = async (req) => {
    try {
        const cid = req.headers['x-cid'];

        if (isEmpty(cid)) {
            return {
                status: 400,
                code: 'MS00031',
                result: 'O condomínio é obrigatório',
            };
        }
    } catch (e) {
        console.error(e);
        return {
            status: 500,
            code: 'MS00030',
            result:
                typeof e === 'string'
                    ? e
                    : 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};

// Tipos de relatório
// Boleto
// Despesas
// Receitas
// DRE
// Morador/Unid
// Acordo
// E-Mail
// Portaria
// Contas Bancárias
// Demonstrativo p/ Balancete
// Livro Diário de Lançamento
// Inadimplência
