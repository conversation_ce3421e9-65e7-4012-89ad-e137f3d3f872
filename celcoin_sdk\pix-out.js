const { celcashHeader } = require('./connect-cel-cash');
const { http } = require('./http');

module.exports = async (data) => {
    try {
        let { resp, err } = await http(
            'get',
            `/company/subaccounts?documents=${data.condRegister}&limit=100&startAt=0`
        );

        if (err) {
            console.log('Deu erro ao tentar buscar o condominio na celcash');
            console.log(err.response.data);
            throw err;
        }
        if (resp.SubAccounts.length == 0) {
            console.log('Condomínio não encontrado na celcoin');
            throw 'Condomínio não encontrado na celcoin';
        }
        const { galaxId, galaxHash } = resp.SubAccounts[0].ApiAuth;
        if (!galaxId || !galaxHash) {
            console.log('Não foi encontrado o hash ou id na celcoin');
            throw 'galaxID ou galaxHash não encontrados!';
        }
        const { header, ch_error } = await celcashHeader(galaxId, galaxHash);
        if (ch_error) {
            console.log('Deu erro gerando o token na celcoin');
            throw ch_error;
        }

        delete data.condRegister;

        const { resp: res, err: er } = await http(
            'post',
            '/pix/payment',
            header,
            data
        );
        if (er) {
            console.log('Deu erro criando o boleto');
            throw er;
        }

        return { result: res.Payment };
    } catch (e) {
        console.log('Error ao tentar realizar um pix!!');
        return { po_error: e };
    }
};
