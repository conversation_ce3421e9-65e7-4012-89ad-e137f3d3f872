const {
    getAgreementByID,
    updateTransaction,
    getTransactionByID,
    transactionSituation,
    getCondByID,
    now,
    agreementSituation,
    isRegistered,
    updateAgreement,
} = require('../../../lib');
const { cancelBankslip } = require('../../../celcoin_sdk');

// Função responsável pelo cancelamento do acordo
module.exports = async (req) => {
    try {
        console.log('Cancelamento de acordo...');
        const { body, headers } = req;
        const cid = headers['x-cid'];

        if (!cid) {
            return {
                status: 400,
                code: 'MS00061',
                result: 'O id do condomínio é obrigatório',
            };
        }

        if (!body.agreementId) {
            return {
                status: 400,
                code: 'MS00062',
                result: 'O id do acordo é obrigatório',
            };
        }

        const { condData, gc_error } = await getCondByID(cid);
        if (gc_error) {
            return {
                status: 400,
                code: 'MS00063',
                result: 'Erro ao tentar buscar os dados do condomínio no banco',
            };
        }

        const { agreementData, ga_error } = await getAgreementByID(
            cid,
            body.agreementId
        );
        if (ga_error) {
            console.error('Erro ao tentar buscar acordo');
            console.error(ga_error);
            return {
                status: 500,
                code: 'MS00064',
                result: 'Erro ao tentar buscar acordo',
            };
        }

        const agreeTrans = agreementData.transactionsIds;

        for (let i = 0; i < agreeTrans.length; i++) {
            const tranId = agreeTrans[i];
            console.log('cancelando parcelas...');
            const { transactionData, gt_error } = await getTransactionByID(
                cid,
                tranId
            );
            if (gt_error) {
                console.log('Erro ao tentar buscar a transação:', tranId);
                console.log(gt_error);
                throw 'Erro ao buscar transação';
            }

            if (
                transactionData &&
                transactionData.situation !== transactionSituation.paid &&
                transactionData.situation !== transactionSituation.canceled
            ) {
                if (transactionData.isRegistered) {
                    const { _, cb_error } = await cancelBankslip({
                        condRegister: condData.condRegister,
                        id: tranId,
                    });
                    if (cb_error) {
                        console.log(cb_error);
                        throw (
                            ('Erro ao tentar cancelar a parcela na celcoin: ',
                            tranId)
                        );
                    }

                    const err = await updateTransaction(cid, tranId, {
                        situation: transactionSituation.canceled,
                        updated_at: now(),
                    });
                    if (err) {
                        console.log(err);
                        throw (
                            ('Parcela cancelada mas deu erro ao atualizar o banco de dados: ',
                            tranId)
                        );
                    }
                } else {
                    const err = await updateTransaction(cid, tranId, {
                        situation: transactionSituation.canceled,
                        updated_at: now(),
                    });
                    if (err) {
                        console.log(err);
                        throw (
                            ('Erro ao atualizar parcela no banco de dados: ',
                            tranId)
                        );
                    }
                }
            } else {
                console.log('A parcela:', tranId, 'já foi cancelada ou paga');
            }
        }

        const agreeCharges = agreementData.chargeIds;

        for (let i = 0; i < agreeCharges.length; i++) {
            console.log('reativando parcelas...');
            const charId = agreeCharges[i];

            const err = await updateTransaction(cid, charId, {
                situation: transactionSituation.active,
                isRegistered: isRegistered.no,
                updated_at: now(),
            });
            if (err) {
                console.log(err);
                throw (
                    ('Erro ao tentar reativar a parcela no banco de dados: ',
                    charId)
                );
            }
        }

        console.log('Atualizando acordo pra cancelado...');
        const er = await updateAgreement(cid, body.agreementId, {
            situation: agreementSituation.canceled,
            updated_at: now(),
        });

        if (er) {
            console.log(er);
            'Erro ao tentar atualizar o acordo, tente novamente mais tarde:',
                body.agreementId;
        }

        return {
            status: 200,
            result: 'Acordo cancelado e o boletos restaurados com sucesso',
        };
    } catch (e) {
        console.log(e);
        return {
            status: 500,
            code: 'MS00060',
            result: 'Problema no servidor interno. Tente novamente mais tarde!',
        };
    }
};
