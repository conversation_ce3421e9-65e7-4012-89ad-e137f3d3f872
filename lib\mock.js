const Charge = (data) => {
    return {
        myId: '3384684d-1881-41f8-82da-1a4adf7ce9a9',
        galaxPayId: 19,
        value: data.value,
        paymentLink: 'https://cel.cash/c/45715/d8819d45/b',
        mainPaymentMethodId: 'boleto',
        status: 'active',
        additionalInfo: 'Boleto de cobrança mensal com composição de valor',
        createdAt: '2024-08-21 15:05:43',
        updatedAt: '2024-08-21 15:05:43',
        payedOutsideGalaxPay: false,
        Customer: {
            myId: 'e85a8201-2117-10fc-8bb6-079cb425ead4',
            galaxPayId: 27,
            name: 'Margarida de Souza Franca',
            document: '35034858215',
            createdAt: '2024-08-21 14:52:53',
            updatedAt: '2024-08-21 15:05:43',
            emails: ['<EMAIL>'],
            phones: [21964463627],
            Address: {
                zipCode: '79640100',
                street: '<PERSON><PERSON><PERSON>',
                number: '1260',
                complement: 'Unidade 102, bloco 1',
                neighborhood: 'Interlagos',
                city: 'Três Lagoas',
                state: 'MS',
            },
            ExtraFields: [],
        },
        Transactions: [
            {
                galaxPayId: 36,
                value: data.value,
                payday: data.payday,
                paydayDate: null,
                installment: 1,
                status: 'pendingBoleto',
                statusDescription: 'Em aberto',
                statusDate: '2024-08-21 15:05:44',
                createdAt: '2024-08-21 15:05:44',
                chargeGalaxPayId: 19,
                chargeMyId: '3384684d-1881-41f8-82da-1a4adf7ce9a9',
                ConciliationOccurrences: [],
                Boleto: {
                    pdf: 'https://data.cel.cash/landingpage9397421/boleto/20240869X519M5JECWP3T4XRTK9DWFH21150544',
                    bankLine: '34191092308841491789329988600002998200000061000',
                    bankNumber: *********,
                    barCode: '3419998200000061000109*********7892998860000',
                    bankEmissor: 'itau',
                    bankAgency: '7892',
                    bankAccount: '99886',
                },
                Pix: {
                    reference: 'ec965877-9833-41a5-98f3-4c93b7dabb88',
                    qrCode: '00020101021226960014br.gov.bcb.pix2574qrcode.pix.celcoin.com.br/pixqrcode/v2/cobv/14e0fd8e5f55d95af631b6875274e45204000053039865802BR5924CONDOMINIO DUETTO IGUATE6011Tres Lagoas62070503***6304CA99',
                    image: 'https://data.cel.cash/landingpage9397421/pix/ec965877-9833-41a5-98f3-4c93b7dabb88',
                    page: 'https://cel.cash/q/45715/83a64eba',
                },
            },
        ],
        ExtraFields: [],
        PaymentMethodBoleto: {
            fine: 200,
            interest: 200,
            instructions:
                'Favorecido CONDOMíNIO DUETTO IGUATEMI  / CNPJ: 48.101.749/0001-51',
            deadlineDays: 1,
            documentNumber: data.PaymentMethodBoleto.documentNumber,
        },
    };
};

module.exports = { Charge };
