const { downloadFile } = require('../../lib');

async function buildAttachment(fileData) {
  const attachment = [];

  if (fileData && fileData.file) {
    try {
      const file = await downloadFile(fileData.file);
      attachment.push({
        content: file,
        filename: fileData.name,
        type: fileData.mimeType,
        disposition: 'attachment',
      });
    } catch (e) {
      console.error('Erro ao tentar baixar o arquivo', e);
    }
  }

  return attachment;
}

module.exports = { buildAttachment };
