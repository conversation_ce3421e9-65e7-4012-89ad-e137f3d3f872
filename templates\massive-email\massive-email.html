<!doctype html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Unidade {{ block }} {{ unit }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: #333;
        }

        h1 {
            font-size: 24px;
            margin: auto;
        }

        h2 {
            font-size: 20px;
            margin-top: 10px;
        }

        p {
            margin-bottom: 10px;
        }

        a {
            color: #0073b7;
            text-decoration: none;
        }

        .container {
            width: 1000px;
            margin: auto;
        }

        .header {
            background-color: #0073b7;
            color: #fff;
            padding: 25px;
            text-align: center;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
        }

        .sub-header {
            text-align: center;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
        }

        .sub-text {
            font-size: 15px;
            margin: 0;
        }

        .sub-cont {
            margin: 0;
            text-align: left;
        }

        .subject {
            font-size: 18px;
            margin-left: 80px;
        }

        .logo {
            max-width: 100px;
            max-height: 100px;
        }

        .content {
            padding: 20px;
        }

        .footer {
            background-color: #f7f7f7;
            padding: 10px;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="{{ logo1 }}" id="logo1" class="logo" onerror="this.style.display='none'" />

            <h1>{{ condName }}</h1>

            <img src="{{ logo2 }}" id="logo2" class="logo" onerror="this.style.display='none'" />
        </div>
        <hr />
        <div class="sub-header">
            <div class="sub-cont">
                <b>
                    <p class="sub-text">
                        Bloco/Unidade: {{ block }}-{{ unit }}
                    </p>
                </b>
                <b>
                    <p class="sub-text">Nome: {{ name }}</p>
                </b>
            </div>

            <h1 class="subject">Assunto: {{ subject }}</h1>

            <div class="sub-cont">
                <p class="sub-text">{{ date }}</p>
                <p class="sub-text">{{ email }}</p>
            </div>
        </div>
        <hr />
        <div class="content">
            {{ content }}

            <p>Atenciosamente,</p>

            <p>{{ condName }}</p>
        </div>
        <div class="footer">
            <p>
                &copy; CondoGaia - Software de gestão para condomínios 2024
            </p>
        </div>
    </div>
</body>

</html>