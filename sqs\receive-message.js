const sgMail = require('@sendgrid/mail');
const { v4: uuidv4 } = require('uuid');
const { generateHtml } = require('../pdfs/generate-html'); const { processMassEmail } = require('./emailProcessor');
const {
  generateBankSlip,
  cancelBankslip,
  mainPaymentMethodId,
} = require('../celcoin_sdk');
const {
  createTransaction,
  createSentEmail,
  getLotByID,
  getCondByID,
  getTotalUnitsLenth,
  getPayerByID,
  getPayerCompositionByID,
  getCondBankAccounts,
  getTransactionByID,
  updateLotByID,
  updateTransaction,
  updateOurNumber,
  updateAgreement,
  now,
  currency,
  lotSituation,
  transactionSituation,
  isRegistered,
  bankslipTypes,
  sqsProcessType,
  lotEmailReceiverType,
  deleteMessage,
  receiveMessage,
  emailStatus,
  states,
  mask,
  emailTemplates,
  newOurNumber,
  deletePayerComposition,
  newMaturity,
  convertDate,
  agreementSituation,
} = require('../lib');
const { default: axios } = require('axios');

// Responsável pelo recebimento de mensagens das filas de boleto em massa e avulso
module.exports = async () => {
  try {
    await receiveMessage(async (message) => {
      const { lot_uuid, cond_uuid, process_type, config } = JSON.parse(
        message.Body
      );

      let err = undefined;

      err = await deleteMessage(message);
      if (err) {
        console.log('Deu erro ao tentar apagar a mensagem');
        console.log(err);
      }

      const { lotData, gl_error } = await getLotByID(
        cond_uuid,
        lot_uuid,
        process_type
      );

      if (gl_error || !lotData || !lotData.created_at) {
        console.log(
          'Deu erro buscando o lote. ID do lote:',
          lot_uuid,
          ' ID do condominio:',
          cond_uuid
        );
        console.log(gl_error);

        return;
      }

      const { condData, gc_error } = await getCondByID(cond_uuid);
      if (gc_error) {
        console.log(
          'Deu erro buscando o condominio. ID do condominio:',
          cond_uuid
        );
        console.log(gc_error);
        return;
      }

      if (
        process_type !== sqsProcessType.emails &&
        process_type !== sqsProcessType.agreement &&
        (!lotData.itens || lotData.itens.length === 0)
      ) {
        console.log('Não tem dados para processar no lote');
        console.log(lotData);
        return;
      }

      if (process_type === sqsProcessType.monthly) {
        console.log('Processando um lote de boleto mensal');
        console.table({
          lot_uuid,
          cond_uuid,
          process_type,
          config,
        });

        const lotDataItens = lotData.itens.slice(
          config.offset,
          config.limit
        );

        let valuePerUnit = 0; // valor a ser pago por unidade em caso de Rateio de conta

        if (condData.chargeType === 'Rateio de contas') {
          const { itens, err } = await getTotalUnitsLenth(cond_uuid);

          if (err) {
            console.log(
              'Deu erro buscando as unidades do condominio. ID do condominio:',
              cond_uuid
            );
            console.log(err);
            return;
          }

          const divisionValue = condData.divisionQuota;

          valuePerUnit = parseInt(divisionValue / itens);
        }

        // items processados do lote
        let lotProcess = 1;

        for (let i = 0; i < lotDataItens.length; i++) {
          const item = lotDataItens[i];

          const { payerData, gp_error } = await getPayerByID(
            cond_uuid,
            item
          );

          if (gp_error) {
            console.log(
              'Deu erro buscando o pagador. ID do pagador:',
              payerData,
              ' ID do condominio:',
              cond_uuid
            );
            console.log(gp_error);
            return;
          }

          const { payerCompositions, gpc_error } =
            await getPayerCompositionByID(cond_uuid, item);

          if (gpc_error) {
            console.log(
              'Deu erro buscando a composição do pagador. ID do pagador:',
              payerData,
              ' ID do condominio:',
              cond_uuid
            );
            console.log(gpc_error);
            return;
          }

          const compositionInfo = {
            total_value: 0,
            data: [],
          };

          if (
            compositionInfo.total_value - condData.discount >=
            500
          ) {
            compositionInfo.total_value =
              compositionInfo.total_value - condData.discount;
          }

          // adicionando o valor por unidade em caso de rateio, se não for vai zerado
          compositionInfo.total_value =
            compositionInfo.total_value + valuePerUnit;

          // colocando os valores da composição no valor do boleto
          payerCompositions.docs.forEach((comp) => {
            const compData = comp.data();

            compositionInfo.total_value =
              compositionInfo.total_value + compData.value;

            compositionInfo.data.push(compData);
          });

          const ourNumber = newOurNumber(condData.our_number, i + 1);

          const payload = {
            value: compositionInfo.total_value,
            additionalInfo: 'Boleto de cobrança mensal',
            payday: lotData.maturity,
            mainPaymentMethodId: mainPaymentMethodId.boleto,
            Customer: {
              myId: payerData.myId,
              name: payerData.name,
              document: payerData.document,
              emails: payerData.emails,
              phones: payerData.phones,
              Address: {
                zipCode: payerData.Address.zipCode,
                street: payerData.Address.street,
                number: payerData.Address.number,
                complement: `Unidade ${payerData.unitName}, bloco ${payerData.unitBlock}`,
                neighborhood: payerData.Address.neighborhood,
                city: payerData.Address.city,
                state: payerData.Address.state,
              },
            },
            PaymentMethodBoleto: {
              fine: condData.fine,
              interest: condData.fees,
              deadlineDays: 1,
              documentNumber: ourNumber,
            },
            PaymentMethodPix: {
              fine: condData.fine,
              interest: condData.fees,
              Deadline: {
                type: 'days',
                value: 30,
              },
            },
          };

          const transactionUUID = uuidv4();
          err = await createTransaction(cond_uuid, transactionUUID, {
            ...payload,
            composition_info: compositionInfo,
            situation: transactionSituation.pending,
            payer_id: item,
            lotId: lot_uuid,
            emissionType: bankslipTypes.monthly,
            unitName: payerData.unitName,
            unitBlock: payerData.unitBlock,
            unitId: payerData.unitId,
            isRegistered: isRegistered.yes,
            transactionId: transactionUUID,
            issuer: lotData.issuer || 'sindico',
            created_at: now(),
            updated_at: now(),
          });

          if (err) {
            console.log(
              'Deu erro criando a transação. ID da transação:',
              transactionUUID
            );
            console.log(err);
            return;
          }

          payload.condRegister = condData.condRegister;
          payload.myId = transactionUUID;

          const { result, gb_error } =
            await generateBankSlip(payload);

          if (gb_error) {
            const { data } = gb_error.response;

            let errors = undefined;

            if (!data.error && !data.error.datails) {
              errors = { error: { details: [] } };
            } else {
              errors = data;
            }

            console.log(
              'Error datails:',
              JSON.stringify(errors.error.details)
            );

            err = await updateTransaction(
              cond_uuid,
              transactionUUID,
              {
                situation: transactionSituation.failed,
                error: errors.error,
                isRegistered: isRegistered.error,
                updated_at: now(),
              }
            );

            if (err) {
              console.log(
                'Deu erro atualizando a transação. ID da transação:',
                transactionUUID
              );
              console.log(err);
              return;
            }

            err = await updateLotByID(cond_uuid, lot_uuid, {
              situation: lotSituation.partial_failed,
              updated_at: now(),
              error: {
                payer_id: item,
                transaction_id: transactionUUID,
                info: errors.error,
              },
            });

            if (err) {
              console.log(
                'Deu erro atualizando o lote. ID do lote:',
                lot_uuid
              );
              console.log(err);
              return;
            }

            return;
          }

          const trans = result.Transactions.shift();

          err = await updateTransaction(cond_uuid, transactionUUID, {
            situation: transactionSituation.to_due,
            payment_info: trans,
            isRegistered: isRegistered.yes,
            updated_at: now(),
          });

          if (err) {
            console.log(
              'Deu erro atualizando a transação. ID da transação:',
              transactionUUID
            );
            console.log(err);
            return;
          }

          if (lotData.situation != lotSituation.partial_failed) {
            err = await updateLotByID(cond_uuid, lot_uuid, {
              situation: lotSituation.success,
              updated_at: now(),
            });

            if (err) {
              console.log(
                'Deu erro atualizando o lote. ID do lote:',
                lot_uuid
              );
              console.log(err);
              return;
            }
          }

          err = await updateOurNumber(cond_uuid, ourNumber);

          if (err) {
            console.log(
              `Deu erro atualizando o nosso numero. ID do condomínio: ${cond_uuid}, Nosso numero: ${ourNumber}`
            );
            console.log(err);
            return;
          }

          await updateLotByID(cond_uuid, lot_uuid, {
            count: {
              total: lotData.count.total,
              processed: lotData.count.processed + lotProcess++,
            },
            updated_at: now(),
          });

          if (err) {
            console.log(
              'Deu erro atualizando o lote. ID do lote:',
              lot_uuid
            );
            console.log(err);
            return;
          }

          const { bankAccounts, bc_error } =
            await getCondBankAccounts(cond_uuid);

          if (bc_error || bankAccounts.docs.length === 0) {
            console.log(
              'Deu erro buscando as contas bancárias do condomínio ou nenhuma conta cadastrada'
            );
            console.log(bc_error);
            return;
          }

          let bankAccount = undefined;

          bankAccounts.forEach((acc) => {
            const account = acc.data();
            if (account.main) {
              bankAccount = account;
            }
          });

          if (!bankAccount) {
            console.log('Não foi encontrado nenhuma conta padrão');
            console.log(bankAccount);
            return;
          }

          if (lotData.sendEmail) {
            await sendEmails({
              ...result,
              template_id: condData.billType,
              cond_name: condData.condName,
              cond_register: condData.condRegister,
              maturity: trans.payday,
              unitName: payerData.unitName,
              unitBlock: payerData.unitBlock,
              unitId: payerData.unitId,
              pdf_link: trans.Boleto.pdf,
              subject: 'Boleto de cobrança mensal',
              to: payerData.emails[0],
              condId: cond_uuid,
              emailData: {
                logo: condData.logo || '',
                bank_number: '341',
                digitable_line: mask.digitableLine(
                  trans.Boleto.bankLine
                ),
                due_date: now('DD/MM/YYYY', trans.payday),
                recipient_name: condData.condName,
                recipient_document: mask.document(
                  condData.condRegister
                ),
                recipient_address: `${condData.addressEntity.street}, ${condData.addressEntity.number}`,
                recipient_city: condData.addressEntity.city,
                recipient_state:
                  states[condData.addressEntity.state],
                recipient_uf: condData.addressEntity.state,
                recipient_zipcode: mask.zipcode(
                  condData.addressEntity.zipCode
                ),
                recipient_ag: bankAccount.agencyNumber,
                recipient_code: bankAccount.accountNumber,
                created_at: now('DD/MM/YYYY'),
                document_number: trans.Boleto.bankNumber,
                processed_at: now('DD/MM/YYYY'),
                our_number: ourNumber,
                ammount: currency.format(trans.value / 100),
                instructions:
                  result.PaymentMethodBoleto.instructions,
                payer_name: result.Customer.name,
                payer_document: mask.document(
                  result.Customer.document
                ),
                payer_address: `${result.Customer.Address.street}, ${result.Customer.Address.number}`,
                payer_city: result.Customer.Address.city,
                payer_state:
                  states[result.Customer.Address.state],
                payer_uf: result.Customer.Address.state,
                payer_zipcode: mask.zipcode(
                  result.Customer.Address.zipCode
                ),
                client_name: condData.condName,
                client_document: mask.document(
                  condData.condRegister
                ),
              },
            });
          }

          // colocando os valores da composição no valor do boleto
          payerCompositions.docs.forEach(async (comp) => {
            const compID = comp.id.split(':');

            if (compID[1] !== 'cota' && compID[1] !== 'reserva') {
              err = await deletePayerComposition(
                cond_uuid,
                item,
                comp.id
              );

              if (err) {
                console.log(
                  `Erro ao tentar deletar a composição do pagador: ${item} - Composição: ${comp.id}`
                );
              }
            }
          });

          console.log('transactionID:', transactionUUID);

          console.log(
            'acho que deu tudo certo gerando o boleto em massa, amem'
          );
        }
      } else if (process_type === sqsProcessType.single) {
        console.log('Processando um lote de boleto avulso');
        console.table({
          lot_uuid,
          cond_uuid,
          process_type,
          config,
        });

        const lotDataItens = lotData.itens.slice(
          config.offset,
          config.limit
        );

        // items processados do lote
        let lotProcess = 1;

        for (let i = 0; i < lotDataItens.length; i++) {
          const item = lotDataItens[i];

          const { payerData, gp_error } = await getPayerByID(
            cond_uuid,
            item
          );

          if (gp_error) {
            console.log(
              'Deu erro buscando o pagador. ID do pagador:',
              payerData,
              ' ID do condominio:',
              cond_uuid
            );
            console.log(gp_error);
            return;
          }

          const ourNumber = newOurNumber(condData.our_number, i + 1);

          const dates = convertDates({
            start: lotData.start, // Data de inicio do boleto
            end: lotData.end, // Data final do boleto
            maturity: lotData.maturity, // Data de vencimento do boleto
            reference: lotData.reference, // Mês e ano de refência
            created_at: lotData.created_at,
            updated_at: lotData.updated_at,
          });

          const payload = {
            value: lotData.ammount,
            additionalInfo: `Boleto de cobrança de ${lotData.accountingType}`,
            payday: dates.maturity,
            mainPaymentMethodId: mainPaymentMethodId.boleto,
            Customer: {
              myId: payerData.myId,
              name: payerData.name,
              document: payerData.document,
              emails: payerData.emails,
              phones: payerData.phones,
              Address: {
                zipCode: payerData.Address.zipCode,
                street: payerData.Address.street,
                number: payerData.Address.number,
                complement: `Unidade ${payerData.unitName}, bloco ${payerData.unitBlock}`,
                neighborhood: payerData.Address.neighborhood,
                city: payerData.Address.city,
                state: payerData.Address.state,
              },
            },
            PaymentMethodBoleto: {
              fine: condData.fine,
              interest: condData.fees,
              deadlineDays: 1,
              documentNumber: ourNumber,
            },
            PaymentMethodPix: {
              fine: condData.fine,
              interest: condData.fees,
              Deadline: {
                type: 'days',
                value: 30,
              },
            },
          };

          const transactionUUID = uuidv4();
          err = await createTransaction(cond_uuid, transactionUUID, {
            ...payload,
            situation: transactionSituation.pending,
            accountingType: lotData.accountingType,
            discription: lotData.discription,
            start: dates.start,
            end: dates.end,
            reference: dates.reference,
            image: lotData.image,
            link: lotData.link,
            recurrent: lotData.recurrent,
            payer_id: item,
            lotId: lot_uuid,
            emissionType: bankslipTypes.single,
            unitName: payerData.unitName,
            unitBlock: payerData.unitBlock,
            unitId: payerData.unitId,
            transactionId: transactionUUID,
            issuer: lotData.issuer || 'sindico',
            created_at: now(),
            updated_at: now(),
          });

          if (err) {
            console.log(
              'Deu erro criando a transação. ID da transação:',
              transactionUUID
            );
            console.log(err);
            return;
          }

          payload.condRegister = condData.condRegister;
          payload.myId = transactionUUID;

          if (lotData.isRegistered) {
            const { result, gb_error } =
              await generateBankSlip(payload);

            if (gb_error) {
              const { data } = gb_error.response;

              let errors = undefined;

              if (!data.error && !data.error.datails) {
                errors = { error: { details: [] } };
              } else {
                errors = data;
              }

              console.log(
                'Error datails:',
                JSON.stringify(errors.error.details)
              );

              err = await updateTransaction(
                cond_uuid,
                transactionUUID,
                {
                  situation: transactionSituation.failed,
                  error: errors.error,
                  isRegistered: isRegistered.error,
                  updated_at: now(),
                }
              );

              if (err) {
                console.log(
                  'Deu erro atualizando a transação. ID da transação:',
                  transactionUUID
                );
                console.log(err);
                return;
              }

              err = await updateLotByID(cond_uuid, lot_uuid, {
                situation: lotSituation.partial_failed,
                updated_at: now(),
                error: {
                  payer_id: item,
                  transaction_id: transactionUUID,
                  info: errors.error,
                },
              });

              if (err) {
                console.log(
                  'Deu erro atualizando o lote. ID do lote:',
                  lot_uuid
                );
                console.log(err);
                return;
              }

              return;
            }

            const transaction = result.Transactions.shift();

            err = await updateTransaction(
              cond_uuid,
              transactionUUID,
              {
                situation: transactionSituation.to_due,
                payment_info: transaction,
                isRegistered: isRegistered.yes,
                updated_at: now(),
              }
            );

            if (err) {
              console.log(
                'Deu erro atualizando a transação. ID da transação:',
                transactionUUID
              );
              console.log(err);
              return;
            }

            const { bankAccounts, bc_error } =
              await getCondBankAccounts(cond_uuid);

            if (bc_error || bankAccounts.docs.length === 0) {
              console.log(
                'Deu erro buscando as contas bancárias do condomínio ou nenhuma conta cadastrada'
              );
              console.log(bc_error);
              return;
            }

            let bankAccount = undefined;

            bankAccounts.forEach((acc) => {
              const account = acc.data();
              if (account.main) {
                bankAccount = account;
              }
            });

            if (!bankAccount) {
              console.log(
                'Não foi encontrado nenhuma conta padrão'
              );
              console.log(bankAccount);
              return;
            }

            if (lotData.sendEmail) {
              console.log('Enviando email');
              await sendEmails({
                ...result,
                template_id: condData.billType,
                cond_name: condData.condName,
                cond_register: condData.condRegister,
                maturity: dates.maturity,
                unitName: payerData.unitName,
                unitBlock: payerData.unitBlock,
                unitId: payerData.unitId,
                pdf_link: transaction.Boleto.pdf || '',
                subject: `Boleto de cobrança de ${lotData.accountingType}`,
                to: payerData.emails[0],
                condId: cond_uuid,
                emailData: {
                  logo:
                    condData.logo ||
                    'https://github.com/WilliamSampaio/boleto-itau/blob/master/itau-logo.svg',
                  bank_number: '341',
                  digitable_line: mask.digitableLine(
                    transaction.Boleto.bankLine
                  ),
                  due_date: now(
                    'DD/MM/YYYY',
                    transaction.payday
                  ),
                  recipient_name: condData.condName,
                  recipient_document: mask.document(
                    condData.condRegister
                  ),
                  recipient_address: `${condData.addressEntity.street}, ${condData.addressEntity.number}`,
                  recipient_city: condData.addressEntity.city,
                  recipient_state:
                    states[condData.addressEntity.state],
                  recipient_uf: condData.addressEntity.state,
                  recipient_zipcode: mask.zipcode(
                    condData.addressEntity.zipCode
                  ),
                  recipient_ag: bankAccount.agencyNumber,
                  recipient_code: bankAccount.accountNumber,
                  created_at: now('DD/MM/YYYY'),
                  document_number:
                    transaction.Boleto.bankNumber,
                  processed_at: now('DD/MM/YYYY'),
                  our_number: ourNumber,
                  ammount: currency.format(
                    transaction.value / 100
                  ),
                  instructions:
                    result.PaymentMethodBoleto.instructions,
                  payer_name: result.Customer.name,
                  payer_document: mask.document(
                    result.Customer.document
                  ),
                  payer_address: `${result.Customer.Address.street}, ${result.Customer.Address.number}`,
                  payer_city: result.Customer.Address.city,
                  payer_state:
                    states[result.Customer.Address.state],
                  payer_uf: result.Customer.Address.state,
                  payer_zipcode: mask.zipcode(
                    result.Customer.Address.zipCode
                  ),
                  client_name: condData.condName,
                  client_document: mask.document(
                    condData.condRegister
                  ),
                },
              });
            }
          } else {
            err = await updateTransaction(
              cond_uuid,
              transactionUUID,
              {
                situation: transactionSituation.to_due,
                payment_info: {},
                isRegistered: isRegistered.no,
                updated_at: now(),
              }
            );

            if (err) {
              console.log(
                'Deu erro atualizando a transação. ID da transação:',
                transactionUUID
              );
              console.log(err);
              return;
            }

            const { bankAccounts, bc_error } =
              await getCondBankAccounts(cond_uuid);

            if (bc_error || bankAccounts.docs.length === 0) {
              console.log(
                'Deu erro buscando as contas bancárias do condomínio ou nenhuma conta cadastrada'
              );
              console.log(bc_error);
              return;
            }

            let bankAccount = undefined;

            bankAccounts.forEach((acc) => {
              const account = acc.data();
              if (account.main) {
                bankAccount = account;
              }
            });

            if (!bankAccount) {
              console.log(
                'Não foi encontrado nenhuma conta padrão'
              );
              console.log(bankAccount);
              return;
            }

            if (lotData.sendEmail) {
              console.log('Enviando email');
              await sendEmails({
                ...payload,
                template_id: condData.billType,
                cond_name: condData.condName,
                cond_register: condData.condRegister,
                maturity: dates.maturity,
                unitName: payerData.unitName,
                unitBlock: payerData.unitBlock,
                unitId: payerData.unitId,
                pdf_link: '',
                subject: `Boleto de cobrança de ${lotData.accountingType}`,
                to: payerData.emails[0],
                condId: cond_uuid,
                emailData: {
                  logo:
                    condData.logo ||
                    'https://github.com/WilliamSampaio/boleto-itau/blob/master/itau-logo.svg',
                  bank_number: '341',
                  digitable_line: mask.digitableLine(
                    '12345123451234512121212345121212812345678901112'
                  ),
                  due_date: now('DD/MM/YYYY', dates.maturity),
                  recipient_name: condData.condName,
                  recipient_document: mask.document(
                    condData.condRegister
                  ),
                  recipient_address: `${condData.addressEntity.street}, ${condData.addressEntity.number}`,
                  recipient_city: condData.addressEntity.city,
                  recipient_state:
                    states[condData.addressEntity.state],
                  recipient_uf: condData.addressEntity.state,
                  recipient_zipcode: mask.zipcode(
                    condData.addressEntity.zipCode
                  ),
                  recipient_ag: bankAccount.agencyNumber,
                  recipient_code: bankAccount.accountNumber,
                  created_at: now('DD/MM/YYYY'),
                  document_number: '555555',
                  processed_at: now('DD/MM/YYYY'),
                  our_number: ourNumber,
                  ammount: currency.format(
                    lotData.ammount / 100
                  ),
                  instructions: 'Pagável em qualquer banco',
                  payer_name: payerData.name,
                  payer_document: mask.document(
                    payerData.document
                  ),
                  payer_address: `${payerData.Address.street}, ${payerData.Address.number}`,
                  payer_city: payerData.Address.city,
                  payer_state:
                    states[payerData.Address.state],
                  payer_uf: payerData.Address.state,
                  payer_zipcode: mask.zipcode(
                    payerData.Address.zipCode
                  ),
                  client_name: condData.condName,
                  client_document: mask.document(
                    condData.condRegister
                  ),
                },
              });
            }
          }

          if (item.situation != lotSituation.partial_failed) {
            err = await updateLotByID(cond_uuid, lot_uuid, {
              situation: lotSituation.success,
              updated_at: now(),
            });

            if (err) {
              console.log(
                'Deu erro atualizando o lote. ID do lote:',
                lot_uuid
              );
              console.log(err);
              return;
            }
          }

          await updateLotByID(cond_uuid, lot_uuid, {
            count: {
              total: lotData.count.total,
              processed: lotData.count.processed + lotProcess++,
            },
            updated_at: now(),
          });

          if (err) {
            console.log(
              'Deu erro atualizando o lote. ID do lote:',
              lot_uuid
            );
            console.log(err);
            return;
          }

          err = await updateOurNumber(cond_uuid, ourNumber);
          if (err) {
            console.log(
              `Deu erro atualizando o nosso numero. ID do condomínio: ${cond_uuid}, Nosso numero: ${ourNumber}`
            );
            console.log(err);
          }

          console.log(
            'acho que deu tudo certo gerando o boleto avulso, amem'
          );
        }
      } else if (process_type === sqsProcessType.agreement) {
        console.log('Processando um lote de acordo');
        console.table({
          lot_uuid,
          cond_uuid,
          process_type,
          config,
        });

        for (let i = 0; i < lotData.chargeIds.length; i++) {
          const myID = lotData.chargeIds[i];

          if (!myID || myID.length !== 36) {
            console.log('Id do boleto errado:', myID);
            return;
          }

          const { transactionData, gt_error } =
            await getTransactionByID(cond_uuid, myID);

          if (gt_error) {
            console.log(
              'Erro ao tentar buscar a transação: ',
              myID
            );
            console.log(gt_error);
            return;
          }

          if (transactionData.isRegistered === isRegistered.yes) {
            const { result: _, cb_error } = await cancelBankslip({
              condRegister: condData.condRegister,
              id: myID,
            });

            if (cb_error) {
              if (cb_error.response && cb_error.response.data) {
                console.log(cb_error.response.data);
              } else {
                console.log(cb_error);
              }
              console.log(
                'Ao tentar cancelar o boleto na celcoin, tente novamente mais tarde'
              );
              return;
            }

            console.log('Deletou na celcoin: ', myID);
          }

          const er = await updateTransaction(cond_uuid, myID, {
            situation: transactionSituation.canceled_agreement,
            updated_at: now(),
          });

          if (er) {
            console.log(er);
            console.log(
              'Ao tentar atualizar o boleto no banco, tente novamente mais tarde'
            );
            return;
          }
        }

        const { payerData, gp_error } = await getPayerByID(
          cond_uuid,
          lotData.payerId
        );
        if (gp_error) {
          console.log(gp_error);
          console.log(
            'Erro ao tentar buscar os dados do pagador, tente novamente mais tarde'
          );
          return;
        }

        const ammount = parseInt(
          lotData.totalAmmount / lotData.parcels
        );

        let parcelTransactionIDS = [];

        let attachments = [];

        for (let i = 1; i <= lotData.parcels; i++) {
          const maturity = newMaturity(lotData.maturity, i);
          const ourNumber = newOurNumber(condData.our_number, i);

          const payload = {
            value: ammount,
            additionalInfo: `Boleto de acordo`,
            payday: maturity,
            mainPaymentMethodId: mainPaymentMethodId.boleto,
            Customer: {
              myId: payerData.myId,
              name: payerData.name,
              document: payerData.document,
              emails: payerData.emails,
              phones: payerData.phones,
              Address: {
                zipCode: payerData.Address.zipCode,
                street: payerData.Address.street,
                number: payerData.Address.number,
                complement: `Unidade ${payerData.unitName}, bloco ${payerData.unitBlock}`,
                neighborhood: payerData.Address.neighborhood,
                city: payerData.Address.city,
                state: payerData.Address.state,
              },
            },
            PaymentMethodBoleto: {
              fine: condData.fine,
              interest: condData.fees,
              deadlineDays: 1,
              documentNumber: ourNumber,
            },
            PaymentMethodPix: {
              fine: condData.fine,
              interest: condData.fees,
              Deadline: {
                type: 'days',
                value: 30,
              },
            },
          };

          const transactionUUID = uuidv4();
          let err = await createTransaction(
            cond_uuid,
            transactionUUID,
            {
              ...payload,
              situation: transactionSituation.to_due,
              payerId: payerData.myId,
              lotId: lot_uuid,
              emissionType: bankslipTypes.agreement,
              unitName: payerData.unitName,
              unitBlock: payerData.unitBlock,
              unitId: payerData.unitId,
              isRegistered: isRegistered.no,
              transactionId: transactionUUID,
              issuer: lotData.issuer || 'sindico',
              created_at: now(),
              updated_at: now(),
            }
          );

          if (err) {
            console.error('Erro gerando transação');
            console.log(err);
            return;
          }

          parcelTransactionIDS.push(transactionUUID);
          console.log('ID da transação: ', transactionUUID);

          if (lotData.isRegistered) {
            payload.myId = transactionUUID;
            payload.condRegister = condData.condRegister;

            const { result, gb_error } =
              await generateBankSlip(payload);

            if (gb_error) {
              console.log(gb_error);
              let data = undefined;
              if (gb_error.response && gb_error.response.data) {
                data = gb_error.response.data;
                console.log('message: ', data.error.message);
                console.log('details: ', data.error.details);
              }

              const er = data
                ? {
                  message: data.error.message,
                  details: data.error.details,
                }
                : { message: '', details: [] };

              err = await updateTransaction(
                cond_uuid,
                transactionUUID,
                {
                  situation: transactionSituation.failed,
                  payment_info: {},
                  error: er,
                  isRegistered: isRegistered.error,
                  updated_at: now(),
                }
              );

              if (err) {
                console.log(
                  'Deu erro ao atualizar a transação no banco '
                );
                console.log(err);

                return;
              }

              console.log(
                'Erro ao tentar gerar o boleto na celcoin, tente novamente mais tarde'
              );

              return;
            }

            const trans = result.Transactions.shift();

            err = await updateTransaction(
              cond_uuid,
              transactionUUID,
              {
                situation: transactionSituation.to_due,
                payment_info: trans,
                isRegistered: isRegistered.yes,
                updated_at: now(),
              }
            );

            if (err) {
              console.log(
                'Erro ao tentar atualizar a transação, tente novamente mais tarde'
              );
              console.log(err);
              return;
            }

            if (lotData.sendEmail) {
              const { bankAccounts, bc_error } =
                await getCondBankAccounts(cond_uuid, false);

              if (bc_error || bankAccounts.docs.length === 0) {
                console.log(
                  'Deu erro buscando as contas bancárias do condomínio ou nenhuma conta cadastrada'
                );
                console.log(bc_error);
                return;
              }

              const bankAccount = bankAccounts.docs[0];

              if (!bankAccount) {
                console.log(
                  'Não foi encontrado nenhuma conta padrão'
                );
                console.log(bankAccount);
                return;
              }

              const htmlKeys = {
                logo: condData.logo || '',
                bank_number: '341',
                digitable_line: mask.digitableLine(
                  trans.Boleto.bankLine
                ),
                due_date: now('DD/MM/YYYY', trans.payday),
                recipient_name: condData.condName,
                recipient_document: mask.document(
                  condData.condRegister
                ),
                recipient_address: `${condData.addressEntity.street}, ${condData.addressEntity.number}`,
                recipient_city: condData.addressEntity.city,
                recipient_state:
                  states[condData.addressEntity.state],
                recipient_uf: condData.addressEntity.state,
                recipient_zipcode: mask.zipcode(
                  condData.addressEntity.zipCode
                ),
                recipient_ag: bankAccount.agencyNumber || '',
                recipient_code: bankAccount.accountNumber || '',
                created_at: now('DD/MM/YYYY'),
                document_number: trans.Boleto.bankNumber,
                processed_at: now('DD/MM/YYYY'),
                our_number: ourNumber,
                ammount: currency.format(trans.value / 100),
                instructions:
                  result.PaymentMethodBoleto.instructions,
                payer_name: result.Customer.name,
                payer_document: mask.document(
                  result.Customer.document
                ),
                payer_address: `${result.Customer.Address.street}, ${result.Customer.Address.number}`,
                payer_city: result.Customer.Address.city,
                payer_state:
                  states[result.Customer.Address.state],
                payer_uf: result.Customer.Address.state,
                payer_zipcode: mask.zipcode(
                  result.Customer.Address.zipCode
                ),
                client_name: condData.condName,
                client_document: mask.document(
                  condData.condRegister
                ),
              };

              console.log('Chaves do boleto: ', htmlKeys);

              const attachment = await generateHtml(
                htmlKeys,
                condData.billType
              );

              if (!attachment || attachment.length === 0) {
                console.log('Erro ao tentar gerar o pdf');
              }

              attachments.push({
                content: attachment,
                filename: `boleto-acordo-${i}-${payload.payday}.pdf`,
                type: 'aplication/pdf',
                disposition: 'attachment',
              });
            }
          } else {
            err = await updateTransaction(
              cond_uuid,
              transactionUUID,
              {
                situation: transactionSituation.to_due,
                payment_info: {},
                isRegistered: isRegistered.no,
                updated_at: now(),
              }
            );

            if (err) {
              console.log(
                'Erro ao tentar atualizar a transação, tente novamente mais tarde'
              );
              console.log(err);
              return;
            }
          }

          err = await updateOurNumber(cond_uuid, ourNumber);

          if (err) {
            console.log(
              'Erro ao tentar atualizar o nosso número, tente novamente mais tarde'
            );
            console.log(err);
            return;
          }
        }

        err = await updateAgreement(cond_uuid, lot_uuid, {
          transactionsIds: parcelTransactionIDS,
          situation: agreementSituation.active,
          updated_at: now(),
        });

        if (err) {
          console.log(
            'Erro ao tentar atualizar o acordo, tente novamente mais tarde'
          );
          console.log(err);
          return;
        }

        if (lotData.sendEmail) {
          console.log('Enviando email');
          await sendEmails({
            value: ammount,
            cond_name: condData.condName,
            cond_register: condData.condRegister,
            maturity: now('DD/MM/YYYY', lotData.maturity),
            unitName: payerData.unitName,
            unitBlock: payerData.unitBlock,
            unitId: payerData.unitId,
            pdf_link: '',
            subject: `Boletos referente ao acordo feito no dia ${now('DD/MM/YYYY')}`,
            to: payerData.emails[0],
            payer_name: payerData.name,
            condId: cond_uuid,
            attachments,
          });
        }
      } else if (process_type === sqsProcessType.cancel_agreement) {
        console.log('Processando um lote de cancelamento de acordo');
        console.table({
          lot_uuid,
          cond_uuid,
          process_type,
          config,
        });
        // TODO
      } else if (process_type === sqsProcessType.emails) {
        console.log('Processando um lote de email');
        console.table({
          lot_uuid,
          cond_uuid,
          process_type,
          config,
        });

        await processMassEmail(cond_uuid, lot_uuid, lotData, condData);
      } else {
        console.log(
          'Tipo de processamento do sqs inválido:',
          process_type
        );
      }
    });
  } catch (e) {
    console.log(
      'Deu erro ao tentar executar a função de receber as mensagens do sqs'
    );
    console.log(e);
  }
};

// Manda email para o usuário e salva o email no banco de dados
const sendEmails = async (data) => {
  try {
    console.log('Enviando email...');
    sgMail.setApiKey(process.env.CONDOGAIA_EMAIL);
    const emailId = uuidv4();

    const attachment = await generateHtml(
      { ...data.emailData },
      data.template_id
    );

    if (!attachment || attachment.length === 0) {
      throw 'Erro ao tentar gerar o pdf';
    }

    const msg = {
      // to: '', // payer email
      to: data.to, // payer email
      from: {
        email: '<EMAIL>',
        name: 'Condogaia',
      },
      subject: data.subject,
      templateId: emailTemplates.bankslip,
      dynamicTemplateData: {
        subject: data.subject,
        condName: data.cond_name,
        block: data.unitBlock,
        unit: data.unitName,
        ownerName: data.emailData.payer_name,
        ammount: currency.format(data.value / 100),
        maturity: data.emailData.due_date,
        link: data.pdf_link,
      },
      category: `${data.condId}:${emailId}`,
      attachments: [
        {
          content: attachment,
          filename: `boleto-${data.maturity}.pdf`,
          type: 'aplication/pdf',
          disposition: 'attachment',
        },
      ],
    };

    const dbSave = {
      emailId: emailId,
      unitId: data.unitId,
      unitName: data.unitName,
      unitBlock: data.unitBlock,
      receiverName: data.Customer.name,
      receiverType: lotEmailReceiverType.all,
      emailModel: 'Boleto',
      email: data.to,
      topic: 'Boleto',
      subject: data.subject,
      text: 'Texto livre',
      situation: emailStatus.sent,
      file: data.pdf_link || '',
      clicked: false,
      opened: false,
      sentAt: now(),
      created_at: now(),
      updated_at: now(),
    };

    // Envia email e salva no firestore
    sgMail.send(msg).then(
      async () => {
        const err = await createSentEmail(data.condId, emailId, dbSave);

        if (err) {
          console.log(
            'O email foi enviado mas deu erro ao salvar no banco'
          );
          console.log(err);
          return;
        }

        console.log('Email enviado e salvo no banco. ID:', emailId);
      },
      (error) => {
        console.error(error);
        if (error.response) {
          console.log('Deu erro ao enviar o email');
          throw error.response.body;
        }
      }
    );
  } catch (e) {
    return e;
  }
};

// Converte a data de timestamp pra string bonitinha
const convertDates = (date) => {
  Object.keys(date).forEach((key) => {
    let convertedTime = undefined;

    if (key === 'start' || key === 'end' || key === 'maturity') {
      convertedTime = convertDate(date[key], 'YYYY-MM-DD');
    } else if (key === 'reference') {
      convertedTime = convertDate(date[key], 'YYYY-MM');
    } else {
      convertedTime = convertDate(date[key], undefined);
    }

    date[key] = convertedTime;
  });

  return date;
};
