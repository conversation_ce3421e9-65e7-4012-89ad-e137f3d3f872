const pdf = require('html-pdf');
const {
  mask,
  getTransactionByID,
  isRegistered,
  states,
  currency,
  now,
  getCondByID,
  isEmpty,
  storage,
} = require('../../../lib');

/*
 * Default module exports
 * lotProcess - Função pra retornar a quantidade de itens processados do lote
 */
module.exports = async (req) => {
  try {
    console.log('Api de ver templates rodado...');
    const { body, headers } = req;
    const cid = headers['x-cid'];

    console.log('body: ', body);

    if (!cid) {
      console.log('Id do condomínio é obrigatório');
      return {
        status: 400,
        code: 'MS00071',
        result: 'O id do condomínio é obrigatório',
      };
    }

    const invalid = validate(body);

    if (invalid) {
      return {
        status: 400,
        code: 'MS00072',
        result: invalid,
      };
    }

    const { condData, gc_error } = await getCondByID(cid);
    if (gc_error) {
      return {
        status: 400,
        code: 'MS00073',
        result: 'Erro ao tentar buscar os dados do condomínio no banco',
      };
    }

    let templateData = undefined;

    if (body.type === 'b') {
      const { transactionData, gt_error } = await getTransactionByID(
        cid,
        body.transactionId
      );
      if (gt_error) {
        return {
          status: 500,
          code: 'MS00074',
          result: 'Erro ao tentar buscar a transação, tente novamente mais tarde!',
        };
      }

      if (transactionData.isRegistered === isRegistered.yes) {
        templateData = {
          name: `template-boleto-${body.templateId}`,
          keys: {
            logo: 'https://github.com/WilliamSampaio/boleto-itau/blob/master/itau-logo.svg',
            bank_number: '341',
            digitable_line: mask.digitableLine(
              transactionData.payment_info.Boleto.bankLine
            ),
            due_date: now('DD/MM/YYYY', transactionData.payday),
            recipient_name: condData.condName,
            recipient_document: mask.document(
              condData.condRegister
            ),
            recipient_address: `${condData.addressEntity.street}, ${condData.addressEntity.number}`,
            recipient_city: condData.addressEntity.city,
            recipient_state: states[condData.addressEntity.state],
            recipient_uf: condData.addressEntity.state,
            recipient_zipcode: mask.zipcode(
              condData.addressEntity.zipCode
            ),
            recipient_ag:
              transactionData.payment_info.Boleto.bankAgency,
            recipient_code:
              transactionData.payment_info.Boleto.bankAccount,
            created_at: now('DD/MM/YYYY'),
            document_number:
              transactionData.payment_info.Boleto.bankNumber,
            processed_at: now('DD/MM/YYYY'),
            our_number:
              transactionData.PaymentMethodBoleto.documentNumber,
            ammount: currency.format(transactionData.value / 100),
            instructions:
              transactionData.PaymentMethodBoleto.instructions,
            payer_name: transactionData.Customer.name,
            payer_document: mask.document(
              transactionData.Customer.document
            ),
            payer_address: `${transactionData.Customer.Address.street}, ${transactionData.Customer.Address.number}`,
            payer_city: transactionData.Customer.Address.city,
            payer_state:
              states[transactionData.Customer.Address.state],
            payer_uf: transactionData.Customer.Address.state,
            payer_zipcode: mask.zipcode(
              transactionData.Customer.Address.zipCode
            ),
            client_name: condData.condName,
            client_document: mask.document(condData.condRegister),
          },
        };
      } else if (transactionData.isRegistered === isRegistered.no) {
        templateData = {
          name: `template-boleto-${body.templateId}`,
          keys: {
            logo: 'https://github.com/WilliamSampaio/boleto-itau/blob/master/itau-logo.svg',
            bank_number: '341',
            digitable_line: mask.digitableLine(
              '12345123451234512121212345121212812345678901112'
            ),
            due_date: now('DD/MM/YYYY', transactionData.payday),
            recipient_name: condData.condName,
            recipient_document: mask.document(
              condData.condRegister
            ),
            recipient_address: `${condData.addressEntity.street}, ${condData.addressEntity.number}`,
            recipient_city: condData.addressEntity.city,
            recipient_state: states[condData.addressEntity.state],
            recipient_uf: condData.addressEntity.state,
            recipient_zipcode: mask.zipcode(
              condData.addressEntity.zipCode
            ),
            recipient_ag: '0001',
            recipient_code: '12345-6',
            created_at: now('DD/MM/YYYY'),
            document_number:
              transactionData.PaymentMethodBoleto.documentNumber,
            processed_at: now('DD/MM/YYYY'),
            our_number:
              transactionData.PaymentMethodBoleto.documentNumber,
            ammount: currency.format(lotData.ammount / 100),
            instructions: 'Pagável em qualquer banco',
            payer_name: transactionData.Customer.name,
            payer_document: mask.document(
              transactionData.Customer.document
            ),
            payer_address: `${transactionData.Customer.Address.street}, ${transactionData.Customer.Address.number}`,
            payer_city: transactionData.Customer.Address.city,
            payer_state:
              states[transactionData.Customer.Address.state],
            payer_uf: transactionData.Customer.Address.state,
            payer_zipcode: mask.zipcode(
              transactionData.Customer.Address.zipCode
            ),
            client_name: condData.condName,
            client_document: mask.document(condData.condRegister),
          },
        };
      }
    } else if (body.type === 'bf') {
      templateData = {
        name: `template-boleto-${body.templateId}`,
        keys: {
          logo: 'https://github.com/WilliamSampaio/boleto-itau/blob/master/itau-logo.svg',
          bank_number: '341',
          digitable_line: mask.digitableLine(
            '12345123451234512121212345121212812345678901112'
          ),
          due_date: '10/10/2025',
          recipient_name: condData.condName,
          recipient_document: mask.document(condData.condRegister),
          recipient_address: `${condData.addressEntity.street}, ${condData.addressEntity.number}`,
          recipient_city: condData.addressEntity.city,
          recipient_state: states[condData.addressEntity.state],
          recipient_uf: condData.addressEntity.state,
          recipient_zipcode: mask.zipcode(
            condData.addressEntity.zipCode
          ),
          recipient_ag: '0001',
          recipient_code: '12345-6',
          created_at: now('DD/MM/YYYY'),
          document_number: '********',
          processed_at: now('DD/MM/YYYY'),
          our_number: '********',
          ammount: 'R$ 100,00',
          instructions: 'Pagável em qualquer banco',
          payer_name: 'Fulano da silva',
          payer_document: '123.456.789-01',
          payer_address: `Rua do pagador, 1`,
          payer_city: 'São Paulo',
          payer_state: states['SP'],
          payer_uf: 'SP',
          payer_zipcode: '12345-678',
          client_name: condData.condName,
          client_document: mask.document(condData.condRegister),
        },
      };
    } else if (body.type === 'e') {
      const keys = {
        block: 'A',
        unit: '101',
        logo1: condData.logo1 || '',
        logo2: condData.logo2 || '',
        name: 'Fulano Da Silva',
        subject: 'Exemplo de assunto',
        date: now('DD/MM/YYYY'),
        email: '<EMAIL>',
        content: 'Esse é um exemplo de como vai ficar o seu email',
        condName: condData.condName,
      };

      const doc = await createPDF(keys, 'e');
      if (typeof doc !== 'string') {
        console.log('Doc:', doc);
        throw 'Erro ao tentar gerar o pdf';
      }

      return {
        status: 200,
        result: { pdf: doc, name: `template-email` },
      };
    }

    const pdf = await createPDF({ ...templateData.keys }, body.type, body.templateId);

    if (!pdf || pdf.length === 0 || typeof pdf !== 'string') {
      throw 'Erro ao tentar gerar o pdf';
    }

    return {
      status: 200,
      result: { pdf, name: templateData.name },
    };
  } catch (e) {
    console.error(e);
    return {
      status: 500,
      code: 'MS00070',
      result: 'Problema no servidor interno. Tente novamente mais tarde!',
    };
  }
};

const validate = (body) => {
  if (isEmpty(body)) {
    console.log('O body da request ta vazio', body);
    return 'O body da requisição está inválido';
  }

  // body = {
  //     type: 'b | bf | e', // -> Boleto | Boleto fake | Email
  //     templateId: 1, // se for boleto - decidir o nome dos templates
  //     transactionId: 'id da transação', // se for boleto
  // };

  if (body.type === 'b' || body.type === 'bf') {
    if (isEmpty(body.templateId)) {
      return 'O template é obrigatório';
    }
    if (body.type === 'b' && isEmpty(body.transactionId)) {
      return 'O transactionID é obrigatório para esse tipo';
    }
  } else if (body.type === 'e') {
  } else {
    return 'Tipo de visualização inválido';
  }

  return undefined;
};

const createPDF = async (keys, type, templateId) => {
  return new Promise(async (resolve, reject) => {
    try {
      type = (type || '').trim().toLowerCase();
      let path;

      if (type === 'e') {
        path = 'html-models/massive-email.html';
      } else if (type === 'bf') {
        if (!templateId) return reject('templateId é obrigatório para tipo "bf"');

        const trimmedTemplateId = templateId.trim();
        const folder = trimmedTemplateId.split(' ')[0].toLowerCase(); // 'Avulso 2' → 'avulso'

        path = `html-models/${folder}/${trimmedTemplateId}.html`; // 'html-models/avulso/Avulso 2.html'
      } else {
        path = 'html-models/main.html';
      }

      const file = await storage.file(path);
      const [buffer] = await file.download();

      let template = buffer.toString();

      Object.keys(keys).forEach((key) => {
        template = template.replace(`{{ ${key} }}`, keys[key] || '');
      });

      const options = {
        format: 'A4',
        orientation: 'landscape',
        type: 'pdf',
      };

      pdf.create(template, options).toBuffer((err, buffer) => {
        if (err) return reject(err);
        resolve(buffer.toString('base64'));
      });
    } catch (e) {
      console.log('Erro ao gerar PDF:', e);
      reject(e);
    }
  });
};
