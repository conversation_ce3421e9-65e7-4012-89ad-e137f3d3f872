const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const { getIdempotencyKey } = require('./firestore');
const { cnpj, cpf } = require('cpf-cnpj-validator');
const TZOffset = process.env.TZOffset || -3;
const puppeteer = require('puppeteer');
const axios = require('axios');
const xlsx = require('xlsx');

dayjs.extend(utc);

const now = (format = undefined, date = undefined) => {
  if (format && date) {
    return dayjs(date).utc().add(TZOffset, 'hour').format(format);
  }

  if (date) {
    return dayjs(date)
      .utc()
      .add(TZOffset, 'hour')
      .format('YYYY-MM-DDTHH:mm:ss.SSS');
  }

  if (format) {
    return dayjs().utc().add(TZOffset, 'hour').format(format);
  }

  return dayjs()
    .utc()
    .add(TZOffset, 'hour')
    .format('YYYY-MM-DDTHH:mm:ss.SSS');
};

const validateDate = (date) => {
  return dayjs(date).isValid();
};

const currency = new Intl.NumberFormat('pt-BR', {
  style: 'currency',
  currency: 'BRL',
});

const validIdempotencyKey = async (cond_id, idempotency_key) => {
  try {
    const { itens, err } = await getIdempotencyKey(
      cond_id,
      idempotency_key
    );

    if (err) {
      throw err;
    }

    if (itens > 0) {
      console.log('Chave de idempotencia já está em uso');
      return false;
    }

    return true;
  } catch (e) {
    console.error('Erro ao tentar verificar chave de idempotencia');
    console.error(e);
    return false;
  }
};

const formatDateValue = (date) => {
  const ar = date.split('-');

  return [(ar[2], ar[1], ar[0])].join('/');
};

const mask = {
  cnpj: (v) => {
    v = v.replace(/\D/g, '');
    v = v.replace(/^(\d{2})(\d)/, '$1.$2');
    v = v.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
    v = v.replace(/\.(\d{3})(\d)/, '.$1/$2');
    v = v.replace(/(\d{4})(\d)/, '$1-$2');
    return v;
  },
  cpf: (v) => {
    v = v.replace(/\D/g, '');
    v = v.replace(/(\d{3})(\d)/, '$1.$2');
    v = v.replace(/(\d{3})(\d)/, '$1.$2');
    v = v.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
    return v;
  },
  digitableLine: (barcode) => {
    /*
          Posição 01-03 = Identificação do banco (exemplo: 001 = Banco do Brasil)
          Posição 04-04 = Código de moeda (exemplo: 9 = Real)
          Posição 05-09 = 5 primeiras posições do campo livre (posições 20 a 24 do código de barras)
          Posição 10-10 = Dígito verificador do primeiro campo
          Posição 11-20 = 6ª a 15ª posições do campo livre (posições 25 a 34 do código de barras)
          Posição 21-21 = Dígito verificador do segundo campo
          Posição 22-31 = 16ª a 25ª posições do campo livre (posições 35 a 44 do código de barras)
          Posição 32-32 = Dígito verificador do terceiro campo
          Posição 33-33 = Dígito verificador geral (posição 5 do código de barras)
          Posição 34-37 = Fator de vencimento (posições 6 a 9 do código de barras)
          Posição 38-47 = Valor nominal do título (posições 10 a 19 do código de barras)
        */
    if (barcode.length !== 47) {
      return barcode;
    }

    let mask =
      barcode.substring(0, 5) +
      '.' +
      barcode.substring(5, 10) +
      ' ' +
      barcode.substring(10, 15) +
      '.' +
      barcode.substring(15, 21) +
      ' ' +
      barcode.substring(21, 26) +
      '.' +
      barcode.substring(26, 32) +
      ' ' +
      barcode.substring(32, 33) +
      ' ' +
      barcode.substring(33, 47);

    return mask;
  },
  zipcode: (code) => {
    return `${code.slice(0, 5)}-${code.slice(5, 8)}`;
  },
  document: (document) => {
    if (cpf.isValid(document)) {
      return cpf.format(document);
    }
    if (cnpj.isValid(document)) {
      return cnpj.format(document);
    }
    return document;
  },
};

const createPDF = async (html) => {
  try {
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();

    await page.setContent(html);

    const pdfBuffer = await page.pdf({ format: 'A4', landscape: true });

    const pdfBase64 = Buffer.from(pdfBuffer).toString('base64');

    await browser.close();

    if (pdfBase64 === undefined) {
      setInterval(() => { }, 1000);
    }

    return { doc: pdfBase64 };
  } catch (e) {
    return { err: e };
  }
};

// Gera um novo "Nosso numero"
const newOurNumber = (last_number, index) => {
  let num = parseInt(last_number);
  let zeros = last_number.slice(0, 8 - num.toString().length);

  return zeros + (num + index);
};

// Gera uma nova data de vencimento a partir da atual mais o index do for
const newMaturity = (maturity, index) => {
  const a = maturity.split('-');

  if (parseInt(a[1]) + index <= 12) {
    if (parseInt(a[1]) + index < 10) {
      a[1] = '0' + (parseInt(a[1]) + index).toString();
    } else {
      a[1] = (parseInt(a[1]) + index).toString();
    }
  } else {
    a[0] = (parseInt(a[0]) + 1).toString();
    if (parseInt(a[1]) + index - 12 < 10) {
      a[1] = '0' + (parseInt(a[1]) + index - 12).toString();
    } else {
      a[1] = (parseInt(a[1]) + index - 12).toString();
    }
  }

  if (parseInt(a[1]) === 2 && parseInt(a[2]) > 28) {
    a[2] = '28';
  }

  let newDate = a.join('-');

  while (!validateDate(newDate)) {
    a[2] = (parseInt(a[2]) - 1).toString();
    newDate = a.join('-');
  }

  return newDate;
};

// Converte a data de timestamp pra string bonitinha
const convertDate = (date, format) => {
  const dt = new Date(date._seconds * 1000 + date._nanoseconds / 1000000);

  return now(format, dt);
};

const isEmpty = (value) => {
  // Verifica se o valor é null ou undefined
  if (value === null || value === undefined) {
    return true;
  }

  // Verifica strings vazias
  if (typeof value === 'string' && value.trim() === '') {
    return true;
  }

  // Verifica arrays vazios
  if (Array.isArray(value) && value.length === 0) {
    return true;
  }

  // Verifica objetos vazios
  if (typeof value === 'object' && !Array.isArray(value)) {
    return Object.keys(value).length === 0;
  }

  // Verifica números (NaN é considerado vazio)
  if (typeof value === 'number') {
    return isNaN(value);
  }

  // Verifica booleanos (falsos não são considerados vazios, apenas null ou undefined)
  if (typeof value === 'boolean') {
    return false;
  }

  // Se nenhum dos casos acima foi atendido, o valor não é vazio
  return false;
};

const getSheet = async (url) => {
  try {
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'arraybuffer',
    });

    const workbook = xlsx.read(response.data, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    const dataArray = xlsx.utils.sheet_to_json(worksheet, {
      header: 1,
      blankrows: false,
    });

    if (!dataArray || dataArray.length < 2) {
      console.log('Planilha vazia ou não contém cabeçalhos.');
      return { sheet: [] };
    }

    const headerRow = dataArray[0]; // cabeçalhos reais
    const dataRows = dataArray.slice(1); // dados reais

    const headers = [];
    const seenKeys = new Set();

    // Ignorar a primeira coluna (índice 0)
    headerRow.forEach((header, index) => {
      const trimmedHeader = String(header || '').trim();
      if (trimmedHeader !== '') {
        let uniqueKey = trimmedHeader;
        let counter = 1;
        while (seenKeys.has(uniqueKey)) {
          uniqueKey = `${trimmedHeader}_${counter}`;
          counter++;
        }
        seenKeys.add(uniqueKey);
        headers.push({ key: uniqueKey, originalIndex: index }); // +1 porque pulamos a primeira coluna
      }
    });

    if (headers.length === 0) {
      console.log('Linha de cabeçalho da planilha está vazia ou inválida.');
      return { sheet: [] };
    }

    const jsonData = [];

    for (let i = 0; i < dataRows.length; i++) {
      const rowArray = dataRows[i];

      const isRowTrulyEmpty = rowArray.every(cell =>
        cell === undefined || cell === null || String(cell).trim() === ''
      );

      if (isRowTrulyEmpty) {
        continue;
      }

      const rowObject = {};
      let rowHasMeaningfulContent = false;

      headers.forEach(({ key, originalIndex }) => {
        const cell = rowArray[originalIndex];
        const processedCell = cell !== undefined && cell !== null ? String(cell).trim() : '';
        rowObject[key] = processedCell;

        if (processedCell !== '') {
          rowHasMeaningfulContent = true;
        }
      });

      if (rowHasMeaningfulContent) {
        jsonData.push(rowObject);
      }
    }

    return { sheet: jsonData };
  } catch (e) {
    console.error('Erro ao acessar ou processar a planilha:', e);
    return { error: e };
  }
};


const onlyNumbers = (str) => {
  return str.replace(/\D/g, '');
};

const downloadFile = async (fileLink) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axios({
        url: fileLink,
        method: 'GET',
        responseType: 'arraybuffer',
      });

      resolve(response.data.toString('base64'));
    } catch (e) {
      reject(e);
    }
  });
};

module.exports = {
  now,
  validateDate,
  validIdempotencyKey,
  currency,
  formatDateValue,
  mask,
  createPDF,
  newOurNumber,
  newMaturity,
  convertDate,
  isEmpty,
  getSheet,
  onlyNumbers,
  downloadFile,
};
